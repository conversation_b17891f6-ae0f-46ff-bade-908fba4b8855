<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\QueryHandler\Admin;

use App\Entity\Announcement;
use App\Repository\AnnouncementRepository as LegacyAnnouncementRepository;
use App\Tests\V2\Mother\Announcement\Manager\AnnouncementManagerCollectionMother;
use App\Tests\V2\Mother\Announcement\Manager\AnnouncementManagerCriteriaMother;
use App\Tests\V2\Mother\Announcement\Manager\AnnouncementManagerMother;
use App\Tests\V2\Mother\Announcement\Manager\ManagerMother;
use App\V2\Application\Hydrator\Announcement\Manager\AnnouncementManagerHydratorCollection;
use App\V2\Application\Query\Admin\GetAnnouncementManagers;
use App\V2\Application\QueryHandler\Admin\GetAnnouncementManagersHandler;
use App\V2\Domain\Announcement\Exceptions\AnnouncementNotFoundException;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerCollection;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerRepository;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

class GetAnnouncementManagersHandlerTest extends TestCase
{
    private GetAnnouncementManagersHandler $handler;
    private MockObject|AnnouncementManagerRepository $announcementManagerRepository;
    private MockObject|LegacyAnnouncementRepository $legacyAnnouncementRepository;
    private MockObject|AnnouncementManagerHydratorCollection $hydratorCollection;

    protected function setUp(): void
    {
        $this->announcementManagerRepository = $this->createMock(AnnouncementManagerRepository::class);
        $this->legacyAnnouncementRepository = $this->createMock(LegacyAnnouncementRepository::class);
        $this->hydratorCollection = $this->createMock(AnnouncementManagerHydratorCollection::class);

        $this->handler = new GetAnnouncementManagersHandler(
            $this->announcementManagerRepository,
            $this->legacyAnnouncementRepository,
            $this->hydratorCollection
        );
    }

    public function testHandleThrowsExceptionWhenAnnouncementNotFound(): void
    {
        // Arrange
        $criteria = AnnouncementManagerCriteriaMother::withAnnouncementId(1);
        $query = new GetAnnouncementManagers($criteria, true);

        $this->legacyAnnouncementRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with(['id' => 1])
            ->willReturn(null);

        // Act & Assert
        $this->expectException(AnnouncementNotFoundException::class);
        $this->handler->handle($query);
    }

    public function testHandleReturnsCollectionWithoutHydration(): void
    {
        // Arrange
        $criteria = AnnouncementManagerCriteriaMother::withAnnouncementId(1);
        $query = new GetAnnouncementManagers($criteria, false);
        $announcement = $this->createMock(Announcement::class);
        $collection = AnnouncementManagerCollectionMother::create();

        $this->legacyAnnouncementRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with(['id' => 1])
            ->willReturn($announcement);

        $this->announcementManagerRepository
            ->expects($this->once())
            ->method('findBy')
            ->with($criteria)
            ->willReturn($collection);

        $this->hydratorCollection
            ->expects($this->never())
            ->method('hydrate');

        // Act
        $result = $this->handler->handle($query);

        // Assert
        $this->assertSame($collection, $result);
    }

    public function testHandleReturnsCollectionWithHydrationAndSorting(): void
    {
        // Arrange
        $criteria = AnnouncementManagerCriteriaMother::withAnnouncementId(1);
        $query = new GetAnnouncementManagers($criteria, true);
        $announcement = $this->createMock(Announcement::class);

        // Create managers with different names for sorting test
        $managerA = ManagerMother::withName('John', 'Doe');
        $managerB = ManagerMother::withName('Alice', 'Smith');
        $managerC = ManagerMother::withName('Bob', 'Johnson');

        $announcementManagerA = AnnouncementManagerMother::create();
        $announcementManagerA->setManager($managerA);

        $announcementManagerB = AnnouncementManagerMother::create();
        $announcementManagerB->setManager($managerB);

        $announcementManagerC = AnnouncementManagerMother::create();
        $announcementManagerC->setManager($managerC);

        // Collection in unsorted order
        $collection = new AnnouncementManagerCollection([
            $announcementManagerA, // John Doe
            $announcementManagerB, // Alice Smith
            $announcementManagerC, // Bob Johnson
        ]);

        $this->legacyAnnouncementRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with(['id' => 1])
            ->willReturn($announcement);

        $this->announcementManagerRepository
            ->expects($this->once())
            ->method('findBy')
            ->with($criteria)
            ->willReturn($collection);

        $this->hydratorCollection
            ->expects($this->once())
            ->method('hydrate')
            ->with($collection, $this->anything());

        // Act
        $result = $this->handler->handle($query);

        // Assert
        $this->assertInstanceOf(AnnouncementManagerCollection::class, $result);

        // Check that items are sorted alphabetically by name
        $items = $result->all();
        $this->assertEquals('Alice', $items[0]->getManager()->getFirstName()); // Alice Smith
        $this->assertEquals('Bob', $items[1]->getManager()->getFirstName());   // Bob Johnson
        $this->assertEquals('John', $items[2]->getManager()->getFirstName());  // John Doe
    }
}
