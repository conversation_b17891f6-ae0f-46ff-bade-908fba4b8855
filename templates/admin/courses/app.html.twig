{% extends 'base_app.html.twig' %}

{% block head_stylesheets %}
    {{ parent() }}
    {{ encore_entry_link_tags('courses_new_app') }}
{% endblock %}

{% block body_javascript %}
    {{ parent() }}

    	
    <script>
        localStorage.setItem('user-token', '{{ token }}')
        const assetsDir = "{{ asset('assets/chapters/') }}"
        const courseStatus = {{ courseStatus | json_encode | raw }}
        const courseTranslations = {
            course_started_in_period_title: "{{ 'stats.export.filter.course_started_in_period_title'|trans({}, 'messages',  app.user.locale) }}",
            course_finished_in_period_title: "{{ 'stats.export.filter.course_finished_in_period_title'|trans({}, 'messages',  app.user.locale) }}",
            export_success: "{{ 'stats.export.export_success'|trans({}, 'messages',  app.user.locale) }}",
            export_error: "{{ 'stats.export.export_error'|trans({}, 'messages',  app.user.locale) }}",
            export_dir: "{{ 'stats.export.export_dir'|trans({}, 'messages',  app.user.locale) }}"
        }
        let filters = []
        fetch('/admin/api/v1/filters/by-categories')
            .then((res) => res.json())
            .then((data) => {
                filters = (data.data || []).map(item => ({
                    ...item,
                    filters: (item.filters || []).filter(filter => !!filter.name),
                    key: `filter-${item.id}`,
                }))
            })
            .catch(() => {
            })
    </script>
    {{ encore_entry_script_tags('courses_new_app') }}
{% endblock %}
