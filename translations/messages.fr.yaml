menu.title_platform: 'Campus de formation'
complete: Terminé
Show: Voir
Edit: Modifier
Remove: Retirer
Delete: Supprimer
total: Total
'Yes': Oui
'No': Non
Actions: Actions
Clear: Nettoyer
'No results found': 'Aucun résultat trouvé'
Configuration: Configuration
Limit: Limite
Close: Fermer
Save: Économiser
'Save and create other': 'Créer et ajouter un autre'
'Save changes': 'Enregistrer les modifications'
'Save and keep editing': 'Sauvegarder et poursuivre l''édition'
state: État
create: Créer
cancelar: Annuler
back: 'Retourner à'
add: Ajouter
no_content: 'Pas de contenu'
no_result: 'Aucun résultat n''a été trouvé'
configure_simulator: 'Configurer le simulateur'
edit_configure_simulator: 'Modifier la configuration'
configure_success: 'La configuration a été effectuée avec succès'
save_success: 'Enregistrement sauvegardé avec succès'
error_success: 'Une erreur s''est produite lors de l''enregistrement de la fiche'
configure_completed: 'Configuration terminée'
'Created At': Créée
'Created By': 'Créé par'
'Created by': 'Créé par'
'Updated At': 'Mise à jour'
'Updated By': 'Mise à jour par'
'Deleted By': 'Supprimé par'
'Deleted At': Supprimé
menu.courses_managment.title: 'Gestion des cours'
menu.courses_managment.Segments: Segments
menu.courses_managment.categories: Catégories
menu.courses_managment.level: Niveaux
menu.courses_managment.courses: Cours
menu.courses_managment.announcements: 'Appel à candidatures'
menu.courses_managment.nps_question: 'Questions sur les NPS'
menu.courses_managment.opinion_course: 'Avis sur le cours'
menu.help_managment.title: 'Gestion de l''aide'
menu.help_managment.content_help: 'Aide au contenu'
menu.help_managment.categories_help: 'Aide à la catégorie'
menu.users_managment.title: 'Gestion des utilisateurs'
menu.users_managment.users: Utilisateurs
menu.users_managment.managers: Gestionnaires
menu.users_managment.filter: Filtres
menu.news.title: Actualités
menu.stats.title: Statistiques
menu.stats.export: 'Outil Excel'
menu.users.edit_profile: 'Modifier le profil'
form.label.delete: Supprimer
action.save: Economiser
common_areas.created_at: Créée
common_areas.updated_at: 'Mise à jour'
common_areas.deleted_at: 'Mise à jour'
common_areas.created_by: 'Créé par'
common_areas.updated_by: 'Mise à jour par'
common_areas.actions: Actions
common_areas.basic_information: 'Informations de base'
common_areas.edit: Editer
common_areas.delete: Supprimer
common_areas.name: Nom
common_areas.image: Image
common_areas.state: État
common_areas.create: Créer
common_areas.save: Économiser
common_areas.back_list: 'Retour à la liste'
course_category.label_in_singular: 'Catégorie de cours'
course_category.label_in_plural: 'Catégories de cours'
course_category.configureFields.category_name: 'Nom Catégorie'
course_category.configureFields.category_order: 'Catégorie de commande'
course_category.configureFields.translations: Traductions
course.label_in_singular: Cours
course.label_in_plural: Cours
course.back_to_course: 'Retour au cours'
course.configureFields.basic_information: 'Informations de base'
course.configureFields.code: Code
course.configureFields.name: Nom
course.configureFields.description: Description
course.configureFields.basic: 'De base'
course.configureFields.access_level: 'Niveau d''accès'
course.configureFields.clone: Clone
course.configureFields.open: Ouvrir
course.configureFields.open_visible: 'Visible sur Open Campus'
course.configureFields.active: Actif
course.configureFields.categories: Catégories
course.configureFields.profesional_categories: 'Catégories professionnelles'
course.configureFields.image: Image
course.configureFields.chapter: Chapitre
course.configureFields.translation: Traduction
course.configureFields.general_information: 'Informations générales'
course.configureFields.segment: Segment
course.configureFields.category: Catégorie
course.configureFields.thumbnail_url: 'Vignette d''url'
course.configureFields.locale: Langue
course.configureFields.all_seasons: 'Toutes les saisons'
course.configureFields.chapters: Chapitres
course.configureFields.seasons: Saisons
course.configureFields.courses_translate: Traductions
course.configureFields.add_chapter: 'Ajouter un chapitre'
course.configureFields.no_seasons: 'Pas de saison'
course.configureFields.add_seasons: 'Ajouter une saison'
course.configureFields.add_annuncement: 'Ajouter un appel'
course.configureFields.question_modal_translate: 'Voulez-vous vraiment traduire ce cours ?'
course.configureFields.content_modal_translate: 'Cette action créera une copie du cours qui servira de guide pour la traduction dans une autre langue.'
course.configureFields.translate_already: 'Ce cours a déjà une traduction dans cette langue'
course.configureFields.tag_description: 'Séparer les étiquettes en appuyant sur la touche Entrée'
course.configureFields.new: Nouveau
course.configureFields.add_material: 'Ajouter du matériel'
course.configureFields.add_task: 'Ajouter une tâche'
course.configureFields.task: Tâches
course.season_add: 'La saison a été correctement ajoutée'
course.season_update: 'La saison a été correctement mise à jour'
course.season_add_error: 'Une erreur s''est produite lors de l''ajout de la saison'
course.panel.class: 'Détails du cours'
chapter.label_in_plural: Chapitres
chapter.configureFields.title: Titre
chapter.configureFields.course: Cours
chapter.configureFields.type: Type
chapter.configureFields.season: Saison
chapter.configureFields.description: Description
chapter.configureFields.image: Image
chapter.configureFields.image_file: 'Image d''archive'
chapter_type.description.1: '<p>Le chapitre Scorm est très intéressant</p><p>Il nous permet de télécharger un large éventail de contenus générés avec d''autres outils, par exemple des documents, des contenus interactifs ou même des jeux</p>'
chapter_type.description.2: '<p>C''est l''un des chapitres les plus polyvalents.</p><p>Dans la partie gauche, les titres saisis sont affichés, qui servent d''index pour trouver rapidement le contenu et faciliter la lecture</p>'
chapter_type.description.3: '<p>Il s''agit d''un jeu de quiz qui ajoute une composante aléatoire, puisque vous devez compléter les segments d''une roulette pour la réussir.</p><p>Créez une batterie de questions pour renforcer les connaissances que vous avez apprises. Vous pouvez saisir autant de questions que vous le souhaitez et vous pouvez également les accompagner d''images.</p><p>Pour que le jeu fonctionne correctement, il est conseillé d''inclure un minimum de 10 questions.</p> '
chapter_type.description.4: '<p>Ce jeu propose une série de questions comportant un facteur de risque supplémentaire. Après chaque question, les participants ont la possibilité de rester debout et de conserver leur score actuel, ou de prendre le risque de répondre à une question supplémentaire pour obtenir plus de points. Toutefois, en cas de mauvaise réponse, tous les points accumulés jusqu''alors sont perdus.</p>'
chapter_type.description.5: '<p>C''est le plus classique des jeux de chapitre.</p><p>L''idée est de créer une batterie de questions pour renforcer les connaissances apprises. Vous pouvez entrer un nombre illimité de questions accompagnées d''une image et ne comportant qu''une seule réponse correcte.</p>'
chapter_type.description.6: '<p>Triez et tournez les pièces jusqu''à ce qu''elles soient dans la position et l''orientation correctes, sinon elles ne s''emboîteront pas.</p><p>En haut, il y a quatre segments qui correspondent au temps disponible pour réaliser le puzzle. Lorsqu''un segment de temps est écoulé, l''une des questions que nous avons saisies est posée. Si vous répondez correctement aux questions, vous disposerez de plus de temps pour résoudre le puzzle. Le score final dépend d''une combinaison du temps pris pour réaliser le puzzle, du nombre de bonnes réponses et du nombre de questions manquées.</p>'
chapter_type.description.7: '<p>Ce jeu nous présente une énigme qui peut être accompagnée d''une image, pour la résoudre vous devez sélectionner les bonnes lettres avec soin, chaque fois que vous choisissez une lettre qui ne fait pas partie de la réponse la barre de temps est réduite.</p> <p>Le jeu se joue de la même manière qu''un puzzle, mais la barre de temps est réduite chaque fois que vous choisissez une lettre qui ne fait pas partie de la réponse.</p>'
chapter_type.description.8: '<p>Le format PDF étant largement utilisé pour différents types de contenus, tels que des protocoles ou des manuels, les chapitres PDF sont très intéressants, car ils nous permettent de réutiliser du matériel qui a déjà été publié</p>'
chapter_type.description.9: '<p>Les ressources audiovisuelles ont un grand potentiel pédagogique, elles attirent, captent l''attention et suscitent la curiosité</p><p>La plateforme nous permet de choisir comment introduire la vidéo via "url", ou en sélectionnant un fichier qui se trouve sur notre ordinateur. Dans ce dernier cas, nous pouvons joindre un fichier de sous-titres.</p>'
chapter_type.description.10: '<p>Chapitre de type curseur avec images.</p>'
chapter_type.description.11: '<p>Le jeu consiste à associer les mots, dont chacun correspond à une lettre de la roue. Parfois, la solution sera un mot commençant par la lettre, parfois elle contiendra simplement la lettre.</p>'
chapter_type.description.12: '<p>Dans ce jeu, une série de questions sous forme de texte, d''image ou d''une combinaison des deux sera posée sous forme d''affirmations. Il y a deux réponses possibles "Vrai" ou "Faux" et une seule est correcte. Le temps pour résoudre le jeu est limité.</p>'
chapter_type.description.13: '<p>Dans ce jeu, vous devrez résoudre une énigme avant la fin du temps imparti. L''indice est caché derrière une image floue qui s''éclaircit au fur et à mesure que vous avancez dans le jeu. En plus de l''image, il y a aussi une aide supplémentaire sous forme de texte.</p>'
chapter_type.description.14: '<p>Dans ce jeu classique, vous devez disposer les éléments en faisant glisser les blocs dans le bon ordre. La diversité des possibilités en fait un jeu idéal pour les exercices mathématiques et autres défis éducatifs. Idéal pour créer un test mettant à l''épreuve les capacités de raisonnement et d''ordonnancement.</p>'
chapter_type.description.15: '<p>Excellent exercice pour entraîner la mémoire. L''objectif est de trouver les paires correspondantes.</p><p>L''emplacement des cartes est aléatoire, c''est donc un type de chapitre très rejouable.</p>'
chapter_type.description.16: '<p>Dans ce jeu, on vous présentera une série de mots, de phrases ou de concepts que vous devrez associer à la famille ou au groupe correspondant indiqué ci-dessous. Vous testerez vos capacités d''association et votre rapidité d''esprit dans une course contre la montre.</p>'
chapter_type.description.17: '<p>Dans ce jeu de grammaire et d''apprentissage, l''objectif est de remplir les trous dans les phrases avec les bons mots pour tester vos compétences linguistiques et grammaticales, mais ce n''est pas tout ! Ce jeu est polyvalent et peut être utilisé à bien d''autres fins éducatives.</p>'
chapter_type.description.18: '<p>Dans ce jeu, une question ou une énigme vous sera présentée. La tâche consiste à examiner attentivement l''énigme et à utiliser les lettres fournies pour trouver le mot correct. Mais attention, le temps est limité, ce qui signifie que vous devrez être rapide et précis pour gagner.</p>'
chapter_type.description.19: '<p>Dans ce jeu, vous devrez deviner un mot caché en six tentatives au maximum. Chaque tentative consistera à saisir un mot valide et, après chaque tentative, la couleur des carrés changera pour indiquer les lettres correctes et celles qui sont également dans la bonne position.</p>'
chapter_type.description.20: '<p>Ce jeu consiste à trouver des mots cachés dans une recherche de mots. Le but est de marquer une séquence de lettres horizontalement, verticalement ou en diagonale. Les mots peuvent être trouvés dans les deux sens, de gauche à droite ou de droite à gauche. Si la séquence fait partie d''un mot caché, elle sera considérée comme une réponse correcte.</p>'
chapter_type.description.21: '<p>Pendant la lecture d''une vidéo, des questions interactives sont insérées, qui demandent au spectateur d''être attentif au contenu de la vidéo pour y répondre correctement. En bref, la combinaison de la puissance de la vidéo et de l''interactivité du quiz permet d''offrir une expérience d''apprentissage efficace et attrayante.</p>'
chapter_type.add.1: 'Ajouter scorm'
chapter_type.add.2: 'Ajouter du contenu'
chapter_type.add.3: 'Ajouter la roulette'
chapter_type.add.4: 'Ajouter un jeu'
chapter_type.add.5: 'Ajouter un questionnaire'
chapter_type.add.6: 'Ajouter un puzzle'
chapter_type.add.7: 'Ajouter un mot caché'
chapter_type.add.8: 'Ajouter PDF'
chapter_type.add.9: 'Ajouter une vidéo'
chapter_type.add.10: 'Ajouter un curseur'
chapter_type.add.11: 'Ajouter la roulette'
chapter_type.add.12: 'Ajouter un jeu'
chapter_type.add.13: 'Ajouter un jeu'
chapter_type.add.14: 'Ajouter un jeu'
chapter_type.add.15: 'Ajouter un jeu'
chapter_type.add.16: 'Ajouter une catégorisation'
chapter_type.add.17: 'Ajout de lacunes'
chapter_type.add.18: 'Ajouter un jeu'
chapter_type.add.19: 'Ajouter un jeu'
chapter_type.add.20: 'Ajouter la soupe alphabétique'
chapter_type.add.21: 'Ajouter un quiz vidéo'
chapter_type.all: Tous
chapter_type.content: Théorie
chapter_type.games_test: 'Un test ludique'
chapter_type.description_test: 'Description du test'
chapter_type.type: 'Type de chapitre'
chapter.add_pdf: 'Ajouter un pdf'
chapter.chapter.show_video: 'Voir la vidéo'
chapter.message_pdf_success: 'Le pdf a été ajouté avec succès'
chapter.message_pdf_error: 'Une erreur s''est produite lors de l''enregistrement du pdf'
chapter.chapter.materials: Matériaux
chapter.chapter.show_pdf: 'Voir le pdf'
announcements.label_in_singular: 'Appel à candidatures'
announcements.label_in_plural: 'Appels à candidatures'
announcements.configureFields.courses: Cours
announcements.configureFields.start_at: Démarrage
announcements.configureFields.finish_at: Fin
announcements.configureFields.called: appelé
announcements.configureFields.subsidized: Subventionné
announcements.configureFields.subsidizer: Technicien
announcements.configureFields.subsidizer_entity: 'Entité subventionnaire'
announcements.configureFields.subsidized_announcement: 'Appel à subventions'
announcements.configureFields.max_users: 'Nombre maximal d''utilisateurs'
announcements.configureFields.formative_action_type: 'Type d''action de formation'
announcements.configureFields.format: Format
announcements.configureFields.total_hours: 'Nombre total d''heures'
announcements.configureFields.place: Lieu
announcements.configureFields.training_center: 'Centre de formation'
announcements.configureFields.training_center_address: 'Adresse du centre de formation'
announcements.configureFields.training_center_nif: 'Centre de formation NIF'
announcements.configureFields.training_center_phone: 'Numéro de téléphone du centre de formation'
announcements.configureFields.training_center_email: 'E-mail du centre de formation'
announcements.configureFields.training_center_teacher_dni: 'Centre de formation des enseignants DNI'
announcements.configureFields.called_user: 'Les utilisateurs ont appelé'
announcements.configureFields.search: Recherche
announcements.configureFields.announcement_for: 'Appel à candidatures'
announcements.configureFields.search_user_title: 'Recherche d''utilisateurs pour l''appel à candidatures'
announcements.configureFields.placeholder_search_user: 'Recherche d''utilisateurs'
announcements.configureFields.placeholder_search_category: 'Recherche par catégorie'
announcements.configureFields.placeholder_search_department: 'Recherche par département'
announcements.configureFields.placeholder_search_center: 'Recherche par centre'
announcements.configureFields.placeholder_search_country: 'Recherche par pays'
announcements.configureFields.placeholder_search_division: 'Recherche par division'
announcements.configureFields.result_found: 'Résultats trouvés'
announcements.configureFields.clear_result: 'Des résultats clairs'
announcements.configureFields.error_already_called_user: 'Erreur : L''utilisateur a déjà été appelé !'
announcements.configureFields.error_already_called_user_date: 'Erreur : L''utilisateur a déjà été appelé, à une date similaire !'
announcements.configureFields.notified: Notifié
announcements.configureFields.content_course: 'Contenu du cours'
announcements.configureFields.report: Rapport
announcements.configureFields.title_report: 'Rapport des élèves'
announcements.configureFields.direction: Adresse
announcements.configureFields.telephone: Téléphone
announcements.configureFields.nif: FNI
announcements.configureFields.tutor: Tuteur
announcements.configureFields.apt: Apt
announcements.configureFields.time_total: 'Durée totale'
question.label_in_singular: Question
question.label_in_plural: Questions
question.configureFields.question: Question
question.configureFields.random: 'Au hasard'
question.configureFields.answers: Réponses
question.configureFields.image_file: 'Image du fichier'
question.configureFields.question_for: 'Questions pour'
question.configureFields.image_for: 'Image pour'
question.configureFields.add_image_puzzle: 'Ajouter une image de puzzle'
question.configureFields.add_question: 'Ajouter une question'
question.configureFields.see_image: 'Voir l''image'
content.label_in_singular: Contenu
content.label_in_plural: Contenu
content.configureFields.title: Titre
content.configureFields.content: Contenu
content.configureFields.position: Position
content.configureFields.add_content: 'Ajouter du contenu'
content.configureFields.content_for: 'Contenu pour'
question_nps.label_in_singular: 'Question NPS'
question_nps.label_in_plural: 'Questions sur les SNP'
question_nps.configureFields.type: Type
question_nps.configureFields.position: Position
question_nps.configureFields.question: Question
question_nps.configureFields.course: Cours
question_nps.configureFields.name_question: 'Question sur le nom'
question_nps.configureFields.translations: Traductions
opinions.label_in_singular: Avis
opinions.label_in_plural: Opinions
opinions.configureFields.course: Cours
opinions.configureFields.question: Question
opinions.configureFields.to_post: publier
opinions.configureFields.value: Valeur
opinions.configureFields.valoration: Évaluation
help_category.label_in_singular: 'Aide à la catégorie'
help_category.label_in_plural: 'Catégories aide'
help_category.configureFields.category_name: 'Nom de la catégorie'
help_category.configureFields.translations: Traductions
help_text_content.label_in_singular: 'Aide au contenu'
help_text_content.label_in_plural: 'Aide au contenu'
help_text_content.configureFields.category: Catégorie
help_text_content.configureFields.title: Titre
help_text_content.configureFields.text: Texte
help_text_content.configureFields.translations: Traductions
user.label_in_singular: Utilisateur
user.label_in_plural: Utilisateurs
user.configureFields.division: Division
user.configureFields.country: Pays
user.configureFields.category: Catégorie
user.configureFields.departament: Département
user.configureFields.center: Centre
user.configureFields.gender: Genre
user.configureFields.first_name: Nom
user.configureFields.last_name: 'Nom de famille'
user.configureFields.code: Code
user.configureFields.password: 'Mot de passe'
user.configureFields.change_password: 'Modifier le mot de passe'
user.configureFields.courses: Cours
user.configureFields.extra: Extra
user.configureFields.announcements: 'Appels à candidatures'
user.configureFields.extra_fields: 'Champs supplémentaires'
user.configureFields.avatar_image: 'Image de profil'
user.configureFields.new_password: 'Nouveau mot de passe'
user.configureFields.birthdate: 'Date de naissance'
user.configureFields.edit_user: 'Modifier l''utilisateur'
user.configureFields.user_data: 'Données de l''utilisateur'
user.configureFields.stats: Statistiques
user.configureFields.chapter: Chapitres
user.configureFields.ratio_course: 'Rapport Cours/Personnes'
user.configureFields.avg_stars: 'Moyenne des étoiles'
user.configureFields.time: Météo
user.configureFields.chapter_time: 'Temps passé par chapitre'
user.configureFields.available: disponible
user.configureFields.messages: Messages
user.configureFields.login_history: 'Historique de la connexion'
user.configureFields.started_at: Démarrage
user.configureFields.finished_at: Fin
user.configureFields.time_spent: 'Temps passé'
user.configureFields.content_viewed: 'Contenus consultés'
user.configureFields.interaction_with_teacher: 'Interactions avec l''enseignant'
user.configureFields.course_content: 'Contenu du cours'
user.configureFields.content_type: 'Type de contenu'
user.configureFields.finished: Terminé
user.configureFields.teacher_interaction: 'Interactions avec les enseignants'
user.configureFields.date: Date
user.configureFields.sender: Expéditeur
user.configureFields.recipient: Destinataire
user.configureFields.subject: Sujet
user.configureFields.questions: Questions
user.configureFields.chapter_type: 'Type de chapitres'
user.configureFields.finished_chapter_types: 'Types de chapitres achevés'
user.configureFields.button_validate: Valider
user.configureFields.open: 'Campus ouvert'
user.configureFields.computer: Ordinateur
user.configureFields.mobile: Mobile
user.configureFields.tablet: Tablette
user.manage.assign_data: 'Attribuer des données'
user.gender.m: Homme
user.gender.f: Femme
user.configureFields.time_title: 'Dévouement par jour'
user.configureFields.interaction_in_forum: 'Interactions au sein du forum'
user.configureFields.email: Courriel
user.configureFields.fullname: 'Nom et prénom'
user.filtersRequired: 'Sélectionner au moins un filtre'
stats.general_stats: 'Statistiques générales'
stats.total_times_spent: 'Temps total passé'
stats.users_activity: 'Activité des utilisateurs'
stats.users_active_last_30: 'Actifs des 30 derniers jours'
stats.users_inactive_last_30: 'Pas d''activité au cours des 30 derniers jours'
stats.users_never_login: 'Ils ne sont jamais entrés'
stats.daily_chapter: 'Chapitres quotidiens terminés'
stats.finished_chapters: 'Chapitres terminés'
stats.daily_course: 'Cours quotidiens complétés'
stats.finished_courses: 'Cours terminés'
stats.daily_login: 'Connexion quotidienne'
stats.daily_login_tooltip: Connexion
stats.all_courses: 'Tous les cours'
stats.all_countries: 'Tous les pays'
stats.all_centers: 'Tous les centres'
stats.all_categories: 'Toutes les catégories'
stats.all_departament: 'Tous les départements'
stats.all_gender: 'Tous les genres'
stats.all_divisions: 'Toutes les divisions'
stats.filters: Filtres
stats.filter_by: 'Filtrer par'
stats.modal_close: Fermer
stats.clear_filters: 'Nettoyer les filtres'
stats.apply_filters: 'Appliquer les filtres'
stats.export_title: 'Exporter des données'
stats.export.start_date: 'Date de début'
stats.export.end_date: 'Date limite'
stats.export.filename: 'Nom du fichier'
stats.export.request_date: 'Date de la demande'
stats.export.available_until: 'Disponible jusqu''à'
stats.export.loading_data: 'Chargement des données'
stats.export.no_data: 'Pas de données disponibles'
stats.export.download_file: 'Télécharger le fichier'
stats.export.abort_export_request: 'Annuler la demande d''exportation'
stats.export.view_details: 'Voir les détails'
stats.export.reset_form: 'Réinitialiser les champs'
stats.export.error_start_date: 'La date de début ne peut être postérieure à la date de fin.'
stats.export.export_error: 'Une erreur s''est produite lors de la création de votre rapport.'
stats.export.export_success: 'Votre rapport a été ajouté avec succès à la file d''attente des téléchargements.'
stats.export.export_dir: 'Statistiques / Outil Excel'
stats.devices_login: 'Connexion à l''appareil'
stats.distribution_ages: 'Répartition par âge'
stats.generation_babyboom: BabyBoom
stats.generation_x: 'Génération X'
stats.generacion_milenials: 'Les milléniaux'
stats.generacion_z: 'Génération Z'
stats.title_information_user: 'Informations sur l''utilisateur'
stats.title_information_content: 'Informations sur le contenu'
stats.title_information_courses: 'Informations sur les cours'
stats.title_information_chapter: 'Informations sur le chapitre'
stats.distribution_country: 'Répartition par pays'
stats.title_finish_m: terminé
stats.title_made: réalisé
stats.title_made_f: réalisé
stats.chapter_day: jours
stats.chaper_hours: heures
stats.chapter_minutes: minutes
stats.chapter_total: TOTAL
stats.chapter_media: MEDIA
stats.content_active: 'les actifs'
stats.content_active_f: actif
stats.totalLogin: Total
stats.access: Accès
stats.uniqueLogin: Unique
stats.at_least_one_course_finished: 'Utilisateurs formés'
stats.top_rated_courses: 'Cours les mieux notés'
stats.lowest_rated_courses: 'Cours les moins bien notés'
stats.most_completed_courses: 'Cours les plus suivis'
stats.users_more_actives: 'Utilisateurs les plus actifs'
stats.users_less_actives: 'Utilisateurs moins actifs'
stats.accumulative.title: 'Évolution et cumulativité'
stats.accumulative.trained: 'Utilisateurs uniques formés'
stats.accumulative.new: Nouveau
stats.accumulative.accumulated: Accumulé
stats.accumulative.chart: Graphique
stats.accumulative.logins: Connexion
stats.accumulative.courses: Cours
stats.accumulative.courses_started: 'Cours commencés'
stats.accumulative.courses_finished: 'Cours terminés'
stats.accumulative.ratings: Notations
stats.accumulative.time: 'Temps investi (en heures)'
stats.accumulative.filters: 'Distribution des filtres'
'stats. daily_posts': 'Messages quotidiens sur le forum'
stats.most_active_threads: 'Fils de discussion les plus actifs'
stats.most_active_users: 'Utilisateurs les plus actifs'
stats.forum_post_messages_count: Messages
stats.forum_post_title: Titre
task.status.pending: 'En attente'
task.status.in_progress: 'À Progreso'
task.status.success: Terminé
task.status.failure: Erreur
security.login_button_login: Entrer
security.login_button_create_account: 'Créer un compte'
security.login_title: 'Veuillez saisir vos coordonnées'
security.login_remember_me: 'Souvenez-vous de moi'
security.login_question_password: 'Vous avez oublié votre mot de passe ?'
security.button_register: Registre
security.button_exist_accoutn: 'J''ai déjà un compte'
security.button_account: 'Accepter les conditions'
security.first_name: Nom
security.last_name: 'Nom de famille'
security.password: 'Mot de passe'
security.repeat_password: 'Répéter le mot de passe'
security.register: Registre
security.remembered_the_password: 'Vous êtes-vous souvenu de votre mot de passe ?'
security.button_send_email: 'Envoyer un courriel'
security.reset_your_password: 'Réinitialiser votre mot de passe'
security.text_reset_password: 'Saisissez votre adresse électronique et nous vous enverrons un lien pour réinitialiser votre mot de passe.'
course_level.label_in_singular: Niveau
course_level.label_in_plural: Niveaux
component_video.add_package_video: 'Ajouter un paquet vidéo'
component_video.edit_package_video: 'Éditer le paquet vidéo'
component_video.type: Type
component_video.url_video: 'Url vidéo'
component_video.file_subtitle: 'Fichier de sous-titres'
component_video.button_save: Économiser
component_video.text_content_subtitle_video: 'Si vous ajoutez un nouveau sous-titre, il sera remplacé par l''ancien.'
component_video.upload_file_video: 'Sélectionner un fichier vidéo'
component_video.preparing_file: 'Attendre la préparation du dossier'
component_video.package_video: 'Paquet vidéo'
component_video.optimizing_video: 'La vidéo est en cours d''optimisation et sera disponible sous peu.'
component_video.text_good: Bien
filter_category.label_in_singular: 'Catégorie de filtre'
filter_category.label_in_plural: 'Filtrer les catégories'
filter_category.configureFields.name: Nom
filter.label_in_singular: Filtre
filter.label_in_plural: Filtres
filter.configureFields.name: Nom
filter.configureFields.action_add: 'Ajouter un filtre'
filter.extras.no_filters: 'Aucun filtre n''est attribué'
filter.extras.loadings: Chargement...
filter.extras.no_filter_selected: 'Aucun filtre sélectionné'
filter.extras.no_filter_assigned: 'Pas de filtres à assigner'
news.form.title: Titre
news.form.text: Texte
help.pdf.general: ADMIN_ES
help.video.general: '549279910'
segment_category.label_in_singular: 'Segment de catégorie'
segment_category.label_in_plural: 'Segment des catégories'
segment_category.configureFields.name: Nom
course_segmente.label_in_singular: Segment
course_segmente.label_in_plural: Segments
course_segmente.configureFields.name: Nom
course_segmente.configureFields.action_add: 'Ajouter un segment'
documentation.label: Documentation
documentation.title: Titre
documentation.description: Description
documentation.type: Type
documentation.file: Archives
documentation.locale: Langue
pdf.downloadable: Téléchargeable
itinerary.label_in_singular: Itinéraire
itinerary.label_in_plural: Itinéraires
itinerary.name: Nom
itinerary.description: Description
itinerary.tab.courses: Cours
itinerary.tab.users: Utilisateurs
itinerary.no_courses: 'Aucun cours n''a été ajouté à l''itinéraire'
itinerary.no_users: 'Aucun utilisateur n''a été ajouté à l''itinéraire'
itinerary.saving_courses: 'Sauvegarde des cours'
itinerary.find_available_courses: 'Recherche de cours disponibles'
itinerary.find_selected_courses: 'Rechercher les cours sélectionnés'
itinerary.course.position_updated: 'Mise à jour de la position des cours'
itinerary.course.update_warning: 'Les cours de la filière seront mis à jour'
itinerary.user.add_success: 'L''utilisateur a été ajouté avec succès'
itinerary.user.remove_success: 'Utilisateur supprimé avec succès'
itinerary.user.confirm_delete: 'L''utilisateur perd l''accès à l''itinéraire'
itinerary.user.confirm_delete_all: 'L''utilisateur perd l''accès à l''itinéraire'
itinerary.manager.add_success: 'Gestionnaire ajouté avec succès'
itinerary.manager.remove_success: 'Le gestionnaire a été supprimé avec succès'
itinerary.manager.edit_manager: 'Modifier les gestionnaires'
itinerary.manager.find_managers: 'Recherche de managers'
itinerary.manager.confirm_delete: 'Le gestionnaire perd l''accès à l''itinéraire'
itinerary.manager.confirm_delete_all: 'Les gestionnaires perdront l''accès à l''itinéraire'
itinerary.filter.added: 'Filtre ajouté à l''itinéraire'
itinerary.filter.removed: 'Filtre retiré de l''itinéraire'
itinerary.total_courses: 'Total des cours'
common_areas.cancel: Annuler
common_areas.add_all: 'Ajouter tout'
common_areas.remove_all: 'Supprimer tout'
user_filter.modify_users: 'Modifier les utilisateurs'
user_filter.find_by: 'Recherche par'
common_areas.total: Total
common_areas.confirm_delete: "<p style=\"font-size: 14px;\">&lt;p&gt;&lt;b&gt;<span>Voulez-vous vraiment supprimer cet élément ?</span>&lt;/b&gt;&lt;br&gt;</p>\n<p style=\"font-size: 14px;\"><span>Cette action ne peut être annulée.</span>&lt;/p&gt;</p>"
common_areas.confirm_save: 'Voulez-vous vraiment économiser ?'
challenges: Défis
challenges.random: 'Au hasard'
challenges.question: Question
challenges.correct: Correct
challenges.answer1: 'Réponse 1'
challenges.answer2: 'Réponse 2'
challenges.answer3: 'Réponse 3'
challenges.answer4: 'Réponse 4'
challenges.answer5: 'Réponse 5'
challenges.answer6: 'Réponse 6'
material_course.configureFields.type: 'Type de fichier'
material_course.configureFields.save: 'Le matériel a été stocké correctement'
material_course.configureFields.type_1: Pdf
material_course.configureFields.type_2: Vidéo
material_course.configureFields.type_3: Zip/Rar
material_course.configureFields.type_4: Image
material_course.configureFields.type_5: 'Paquets de bureaux'
material_course.configureFields.type_6: Bloc-notes
material_course.configureFields.file: Archives
material_course.configureFields.no_material: 'Sans matériaux ajoutés'
material_course.configureFields.question_delete: 'Voulez-vous vraiment supprimer ce matériel ?'
material_course.configureFields.question_decition: 'Cette action ne peut être annulée'
material_course.configureFields.delete: 'Supprimer le matériel'
material_course.placeholder.file: 'Sélectionner un fichier'
material_course.download: Télécharger
taskCourse.configureFields.noFile: 'Aucun fichier ajouté'
taskCourse.configureFields.question_delete: 'Voulez-vous vraiment supprimer ce fichier ?'
taskCourse.labelInSingular: Tâche
taskCourse.labelInPlural: Tâches
taskCourse.configureFields.dateDelivery: 'Date de livraison'
taskCourse.configureFields.startDate: 'Date de début'
taskCourse.configureFields.visible: Visible
taskCourse.configureFields.senTask: 'La tâche a été envoyée'
taskCourse.configureFields.senTaskUser: 'Envoyé à'
taskCourse.configureFields.addFile: 'Ajouter un fichier'
taskCourse.configureFields.state_0: 'En attente'
taskCourse.configureFields.state_1: Livré
taskCourse.configureFields.state_2: 'En cours d''examen'
taskCourse.configureFields.state_3: Rejeté
taskCourse.configureFields.state_4: Approuvé
taskCourse.configureFields.files_attachment: 'Pièces jointes'
taskCourse.configureFields.sendComment: 'Le commentaire a été envoyé'
taskCourse.configureFields.stateTask: 'La tâche a changé de statut'
taskCourse.configureFields.history: 'L''histoire'
component_game.true_or_false: 'Vrai/faux ou catégoriser'
component_game.adivina_imagen: 'Devinez l''image'
component_game.ordenar_menorMayor: 'Ordre du plus petit au plus grand'
component_game.parejas: 'Par paires'
component_game.rouletteWheel: 'Lettre roulette'
component_game.categorized: Catégoriser
component_game.fillgaps: 'Combler les lacunes'
component_game.guessword: 'Lettres de commande'
component_game.wordle: 'Mot secret'
component_game.lettersoup: 'Soupe à l''alphabet'
component_game.videoquiz: 'Quiz vidéo'
games.letterwheel: 'Lettre roulette'
games.opciones: Options
games.categorize: Catégoriser
games.optiones_empty: 'Vous devez ajouter au moins deux options pour catégoriser.'
games.validate_add_categorize: 'Vous devez sélectionner une réponse correcte et remplir le champ de la question ou sélectionner une image.'
games.add_category: 'Ajouter une option'
games.add_categories: 'Ajouter une catégorie'
games.add_word: 'Ajouter un mot'
games.words: Mots
games.edit_option: 'Modifier l''option'
games.text_common.answer: Réponse
'games.text_common:correct': Correct
games.text_common.time: Météo
games.text_common.word: Mot
games.text_common.no_questions: 'Pas de questions'
games.text_common.text_question: 'Texte de la question'
games.text_common.word_question: 'Mot de la question'
games.text_common.message_guess_word_question: 'Vous devez saisir le texte de la question'
games.text_common.message_guess_word_word: 'Vous devez taper le mot de la question'
games.text_common.message_guess_word_time: 'Vous devez saisir l''heure de la question'
games.text_common.message_guess_word_answer: 'La réponse ne doit contenir qu''un seul mot'
games.text_common.select_image: 'Sélectionner l''image'
games.text_common.ilustre_category: 'Image pour illustrer la catégorie'
games.text_common.ilustre_question: 'Image pour illustrer la question'
games.text_common.message_higher_lower: 'Créez les mots que vous voulez voir apparaître dans le jeu et ordonnez-les comme vous le souhaitez. Vous pouvez modifier l''ordre en faisant glisser les mots.'
games.validate_memory_match: 'Vous devez ajouter un titre ou une image'
games.help: Aide
games.validate_hidden_image: 'Le titre, les mots et l''image sont obligatoires'
games.fillgap.title: 'Comment construire le jeu ?'
games.fillgap.message: 'Dans l''entrée ajouter une phrase ou un espace, vous pouvez créer la structure du jeu et décider si le texte sera une phrase ou un espace, lorsque vous ajoutez un espace dans le résultat de la question, il sera affiché en bleu.'
games.fillgap.result_question: 'Résultat du match'
games.fillgap.word: 'Ajouter une phrase ou une lacune'
games.fillgap.add_filler: 'Ajouter une phrase'
games.fillgap.add_gap: 'Ajouter une lacune'
games.fillgap.new_option: 'Nouvelle option'
games.fillgap.validate_save: 'Vous devez ajouter au moins une phrase, un espace et deux options'
games.videoquiz.message_validate_answer: 'Vous devez ajouter au moins deux réponses et la réponse correcte ne doit pas être vide.'
games.videoquiz.time_video: 'Durée de la vidéo'
games.videoquiz.savell_all_changes: 'Sauvegarder tous les changements'
games.videoquiz.validate_to_add_question: 'Vous devez avoir au moins une question pour pouvoir enregistrer les modifications.'
games.videoquiz.validate_letter_soup: 'Il semble que le titre ou les mots vous manquent'
chapter_type.1: Scorm
chapter_type.2: Contenu
chapter_type.3: Roulette
chapter_type.4: 'Double ou rien'
chapter_type.5: Questionnaire
chapter_type.6: Casse-tête
chapter_type.7: 'Mots cachés'
chapter_type.8: Pdf
chapter_type.9: Vidéo
chapter_type.10: Curseur
chapter_type.11: Roulette
chapter_type.12: 'Vrai ou faux'
chapter_type.13: 'Devinez l''image'
chapter_type.14: Hiérarchie
chapter_type.15: Couples
chapter_type.16: Catégoriser
chapter_type.17: Trous
chapter_type.18: 'Trier les mots'
chapter_type.19: 'Mot secret'
chapter_type.20: 'Soupe à l''alphabet'
chapter_type.21: 'Quiz vidéo'
menu.users.exit_impersonate: 'Sortie de l''usurpation d''identité'
menu.forum: Forum
course.export: 'Cours d''exportation'
course.export.confirm: 'Voulez-vous vraiment exporter les cours ?'
announcements.configureFields.opinions: Opinions
announcements.configureFields.no_messages: 'Aucun message'
announcements.configureFields.info_max_users: 'Le nombre maximum d''utilisateurs qui peuvent être appelés à l''appel est : '
announcements.configureFields.annoucement_all: 'Convoquer tous les'
question_nps.configureFields.source: 'Postulez à'
user.actions.impersonate: 'Usurper l''identité de'
user.show_cv: 'Voir CV'
user.delete_cv: 'Supprimer le CV'
stats.export.download_file_pdf: 'Télécharger le PDF'
stats.export.download_file_xlsx: 'Télécharger Excel'
stats.segmented.title: 'Statistiques segmentées'
filter.removed_filter: 'Le filtre %s a été supprimé avec succès.'
filter.added_filter: 'Le filtre %s a été ajouté avec succès.'
filter.all_removed: 'Les filtres ont été supprimés'
filter.all_added: 'Des filtres ont été ajoutés'
itinerary.chart.users: 'les personnes ont complété l''itinéraire'
itinerary.chart.users_process: 'en cours'
itinerary.chart.users_incomplete: 'sans commencer'
itinerary.chart.users_title: ' personnes assignées'
itinerary.chart.total_time: 'Temps total cumulé'
itinerary.chart.avg_time: 'Temps moyen par personne'
itinerary.chart.by_country: 'Itinéraires par pays'
itinerary.chart.by_hotel: 'Itinéraires par centre'
itinerary.chart.by_department: 'Itinéraires par département'
itinerary.chart.by_grouping: 'Itinéraires par regroupement'
itinerary.users_assign: 'Cet itinéraire a été attribué à des personnes'
itinerary.users.progress: 'Progression de l''itinéraire'
itinerary.users.download_user: 'Télécharger Excel'
itinerary.courses.selected: 'Cours sélectionnés'
itinerary.status.completed: Complété
itinerary.status.started: 'En cours'
itinerary.status.unstarted: 'Pas de départ'
segmented_stats.title1: 'Personnes formées'
segmented_stats.title2: Heures
segmented_stats.title3: Cours
segmented_stats.title4: Accès
segmented_stats.distribution_by_country: 'Répartition par pays'
segmented_stats.structure: Structure
segmented_stats.hotel: Hôtel
segmented_stats.by_department: 'Par département'
segmented_stats.by_school: 'Par l''école'
segmented_stats.total_hours: 'Heures totales'
segmented_stats.total_avg: 'Heures moyennes'
segmented_stats.structure_avg: 'Structure moyenne'
segmented_stats.structure_total: 'Structure totale'
segmented_stats.hotel_avg: 'Hôtel moyen'
segmented_stats.hotel_total: 'Hôtel Total'
segmented_stats.avg: Moyenne
segmented_stats.courses_started: 'Cours commencés'
segmented_stats.courses_finished: 'Cours terminés'
segmented_stats.total_courses_started: 'Total des cours commencés'
segmented_stats.total_courses_finished: 'Total des cours suivis'
segmented_stats.access_totals: 'Total des accès'
segmented_stats.access_uniques: 'Ouverture de session unique'
segmented_stats.certificates: Diplômes
segmented_stats.total_certificates: 'Total des diplômes délivrés'
library.createdAtView: 'Créé par {email} le {date} à {heure}'
library.no_text_provided: 'Aucun texte n''a été saisi'
library.maximum_allowed_size_exceeded: '%s : Le nombre maximal de caractères autorisé a été dépassé.'
library.category.created: 'La catégorie a été créée avec succès'
library.category.updated: 'La catégorie a été mise à jour avec succès'
library.category.deleted: 'La catégorie a été supprimée avec succès'
library.category.activated: 'La catégorie a été activée avec succès'
library.category.deactivated: 'La catégorie a été désactivée avec succès'
library.library.updated: 'La bibliothèque a été mise à jour avec succès'
library.library.created: 'La bibliothèque a été créée avec succès'
library.library.deleted: 'La bibliothèque a été supprimée avec succès'
library.library.name_required: 'Le nom est obligatoire et doit comporter moins de 100 caractères.'
library.library.type_required: 'Le champ "type" est obligatoire'
library.library.link_required: 'Si le type est ''LINK'', une URL valide doit être spécifiée.'
forum.configureFields.thread: 'Fil conducteur'
forum.configureFields.message: Message
forum.configureFields.comment: 'Rapport de commentaires'
forum.configureFields.title_modal_add: 'Ajouter un forum'
forum.configureFields.title_modal_edit: 'Forum d''édition'
course_press.label_in_singular: 'Cours en classe'
course_press.label_in_plural: 'Cours sur site'
menu.courses_managment.course_sections: Sections
common.write: 'Écrire quelque chose'
common_areas.accept: Accepter
common_results: résultats
games.answers: 'Ajouter des réponses'
games.text_common.order_ramdom: 'Trier au hasard'
games.puzzle.description_cropper: 'Sélectionnez la zone de l''image qui apparaîtra dans le puzzle.'
games.validation_truefalse.question_or_image: 'Vous devez taper la question ou sélectionner une image'
games.help.write_question: 'Rédiger une question'
games.help.write_word: 'Écrire un mot'
games.help.write_title: 'Rédiger un titre'
games.help.write_answer: 'Rédiger une réponse'
games.true: Vrai
games.false: Faux
games.edit_video_quiz: 'Visualiser et éditer un quiz vidéo'
games.delete_video_quiz: 'Supprimer le quiz vidéo'
game.feedback.title: 'Ajouter un commentaire (optionnel)'
game.feedback.title_positive: 'Retour d''information en cas de succès'
game.feedback.title_negative: 'Retour d''information en cas de défaillance (optionnel )'
announcements.common.group: Groupe
announcements.common.action_denomination: 'Titre Action'
announcements.common.modality: Modalité
announcements.common.place_of_instruction: 'Lieu de livraison'
announcements.common.collaboration_type: 'Type de collaboration'
announcements.common.provider: Fournisseur
announcements.common.provider_cif: 'CIF Fournisseur'
announcements.observations.costs: Coûts
announcements.observations.course_status: 'Statut du cours'
announcements.observations.comunicado_fundae: 'Communiqué des FUNDAE'
announcements.observations.comunicado_abilitia: 'Communication à ABILITIA'
announcements.observations.economic_module: 'Module économique'
announcements.observations.travel_and_maintenance: 'Déplacement et maintien'
announcements.observations.provider_cost: 'Fournisseur de coûts'
announcements.observations.hedima_management_cost: 'Coût de gestion d''HEDIMA (10%)'
announcements.observations.travel_and_maintenance_cost: 'Frais de voyage et de séjour'
announcements.observations.total_cost: 'Coût total'
announcements.observations.final_pax: 'Finale PAX'
announcements.observations.maximum_bonus: 'Bonus maximum (PAX final)'
announcements.observations.subsidized_amount: 'Montant subventionné'
announcements.observations.private_amount: 'Montant privé'
announcements.observations.provider_invoice_number: 'N° de la facture Fournisseur'
announcements.observations.hedima_management_invoice_number: 'Facture n° HEDIMA Management'
announcements.observations.invoice_status: 'Statut de la facture'
announcements.observations.observations: Remarques
announcements.observations.observation: Remarque
announcements.course.no_chapter: 'Ce cours n''a pas de chapitres, car il s''agit d''un cours en classe.'
announcements.formativeActionTypes.intern: Interne
announcements.formativeActionTypes.extern: Externe
announcements.formativeActionTypes.session_congress: Externe
common_areas.confirm_file_upload: 'Êtes-vous sûr d''avoir téléchargé le(s) document(s) ?'
common_areas.confirm_file_delete: 'Êtes-vous sûr d''effacer ?'
course_section.label_in_singular: Section
course_section.label_in_plural: Sections
course_section.configureFields.name: Nom
course_section.configureFields.description: Description
course_section.configureFields.active: Activa
course_section.configureFields.sort: Commande
course_section.configureFields.translations: Traductions
course_section.configureFields.section_name: 'Nom de la section'
course_section.configureFields.categories: Catégories
user.roles.administrator: Administrateur
user.roles.user: Utilisateur
user.roles.tutor: Tuteur
user.roles.subsidizer: Inspecteur
user.roles.manager: Gestionnaire
user.roles.manager_editor: 'Gestionnaire - Rédacteur'
user.roles.team_manager: 'Chef d''équipe'
survey.label_in_plural: Enquêtes
course.configureFields.is_main: 'Ce cours n''utilisera que ses propres questions pour l''évaluation.'
global.error: 'Une erreur s''est produite. Veuillez réessayer plus tard.'
quiz.configureFields.title_creation: 'Création de questions'
quiz.configureFields.question: 'Formulation de la question'
quiz.configureFields.question_placeholder: 'Rédiger l''énoncé de la question'
quiz.configureFields.question_delete: 'Voulez-vous vraiment éliminer cette question?'
rouletteWord.configureFields.statement: Déclaration
rouletteWord.configureFields.answer: Réponse
rouletteWord.configureFields.type_0: 'Commencer par la lettre'
rouletteWord.configureFields.type_1: 'Contient la lettre'
rouletteWord.configureFields.error.statement.max: 'La déclaration ne peut pas dépasser ${max} caractères.'
rouletteWord.configureFields.error.statement.empty: 'La déclaration ne peut pas être vide'
rouletteWord.configureFields.error.answer.max: 'La réponse ne peut pas dépasser {max} caractères.'
rouletteWord.configureFields.error.answer.empty: 'La réponse ne peut pas être vide'
rouletteWord.configureFields.error.answer.starts: 'La réponse doit commencer par la lettre'
rouletteWord.configureFields.error.answer.includes: 'La réponse doit contenir la lettre'
rouletteWord.response.update_letter: 'Les données ont été correctement mises à jour'
rouletteWord.response.delete_letter: 'Les données ont été supprimées avec succès'
trueorFalse.configureFields.true: Correct
trueorFalse.configureFields.false: Faux
enigma.configureFields.title_creation: 'Création d''énigmes'
puzzle.configureFields.save_image: 'Image sauvegardée avec succès'
puzzle.configureFields.select_correct_answer: 'Vous devez sélectionner une seule réponse correcte'
puzzle.configureFields.recomendation: Recommandation
puzzle.configureFields.recomendation_dimentions: '<p>Nous recommandons les dimensions suivantes <span class="text-primary"><b>minimum de 1024 pixels par côté</b></span> et  <span class="text-primary"><b>maximale de 2000 pixels par côté.</b></span</p>'
puzzle.configureFields.recomendation_description: '<p>Le format du puzzle est <span class="text-primary"><b>carré</b></span>, donc si nous sélectionnons une image avec un rapport d''aspect différent, par exemple une image de paysage, l''outil nous permettra de recadrer en sélectionnant la zone souhaitée.</p>'
hiddenword.configureFields.title: 'Création d''un mot caché'
hiddenword.configureFields.answers_title: 'Mot caché'
hiddenword.configureFields.answers_placeholder: 'Écrire le mot caché'
categorize.configureFields.title_group: 'Groupes ou familles'
fillgaps.configureFields.title: 'Création de phrases'
fillgaps.configureFields.fillgap: Creux
fillgaps.configureFields.fillgaps: Creux
fillgaps.configureFields.type_list: Liste
fillgaps.configureFields.type_drag: Traînée
guesword.configureFields.word_title: 'Mot non ordonné'
guesword.configureFields.word_title_placeholder: 'Écrire un mot qui apparaîtra sans ordre'
guesword.configureFields.solution: Solution
guesword.configureFields.solution_placeholder: 'Ecrire la solution du jeu'
guesword.configureFields.help_placeholder: 'Rédiger une aide pour le jeu'
pairs.configureFields.title: 'Création du jeu'
pairs.configureFields.placeholder_title: 'Rédiger la déclaration'
pairs.configureFields.create_game: 'Création de jeux'
chapter_type.description.22: '<p>La solution idéale pour créer des contenus dynamiques et attrayants dans vos cours ou vos pilules de formation, en présentant l''information de manière visuelle et avec une grande variété d''interactions basées sur le texte, les images, les vidéos, l''audio, les liens multimédias, les cartes interactives, les scènes liées, etc.</p>'
chapter_type.add.22: 'Créer un contenu interactif'
games.videoquiz_exist_question: 'Une question a déjà été créée dans le même créneau horaire.'
chapter_type.22: VCMS
video.configureFields.title: 'Création de quiz vidéo'
video.configureFields.add_question: 'Ajouter une question'
Next: Suivant
hours: Heures
minutes: Minutes
seconds: Secondes
field_required: 'Champ obligatoire'
field_invalid: 'Champ non valide'
field_invalid_format: 'Format non valide'
remaining_characters: 'Caractères restants'
minimiun_characters: 'Nombre minimum de caractères'
menu.home.title: 'Accéder à l''espace utilisateur'
course.season.type.sequential: 'Navigation séquentielle'
course.season.type.free: 'Navigation libre'
course.season.type.exam: 'Mode d''examen'
games.fillgap.add_fillgap: 'Ajouter des espaces en cliquant sur certains mots'
course_section.configureFields.hideCategoryName: 'Masquer le nom de la catégorie'
user.roles.super_administrator: SuperAdministrateur
settings.menu.label: Configuration
settings.header.title: Configuration
setting.menu.general: Général
setting.menu.catalog: Catalogues
share: Partager
report.announcement.participants: 'Les participants'
report.announcement.groupCode: 'Code du groupe'
report.announcement.enterpriseProfile: 'Profil de l''entreprise'
report.announcement.file: Fichier
report.announcement.totalStudents: 'Nombre total d''étudiants'
report.announcement.enterpriseCIF: 'Numéro de TVA de l''entreprise'
report.announcement.advisor: Tuteur
course.stats.started: Commencé
course.stats.ended: Terminé
course.stats.total_time: 'Durée totale'
course.stats.avg_time: 'Durée moyenne'
course.stats.minutes: minutes
course.stats.minute: minute
course.stats.hours: heures
course.stats.hour: temps
course.stats.second: deuxième
course.stats.seconds: secondes
question.configureFields.quantity_max_question: 'Nombre maximum de questions à afficher :'
user.configureFields.available_courses: 'Cours disponibles'
user.configureFields.available_chapter: 'Chapitre disponible'
user.configureFields.courses_stats.finished: terminés
user.configureFields.courses_stats.started: 'a commencé'
user.configureFields.courses_stats.available: disponible
user.configureFields.courses_stats.sent_messages: 'envoyé à'
user.configureFields.courses_stats.received_messages: reçu
user.configureFields.courses_stats.others: Autres
user.configureFields.permissions: Permis
course.configureFields.segments: Segments
stats.roles: Rôles
course.configureFields.language: Langue
game.feedback.wrong: 'Exemple : Wow !'
chapter_type.description.23: '<p>Le jeu de rôle est une activité dans laquelle les participants incarnent et agissent en tant que personnages fictifs, souvent dans un cadre ou un contexte spécifique. Pendant le jeu de rôle, les participants adoptent temporairement la personnalité, les caractéristiques et les comportements des personnages qu''ils incarnent, interagissant les uns avec les autres en fonction des circonstances et de l''environnement imaginaire établi. Cette pratique est utilisée dans divers contextes, tels que les jeux, la thérapie, les simulations éducatives et les activités récréatives, dans le but de favoriser la créativité, l''empathie, la résolution de problèmes et l''exploration de situations hypothétiques.</p>'
password.uppercase: 'Obligatoire 1 ou plusieurs caractères en majuscules'
password.number: '1 ou plusieurs chiffres requis'
password.minimum: 'Le mot de passe doit comporter au moins %s caractères.'
password.disable_3_consecutive_chars: 'Il n''est pas permis de répéter un personnage plus de trois fois de suite.'
password.lowercase: '1 ou plusieurs caractères minuscules requis'
chapter_type.23: 'Jeu de rôle'
chapter_type.add.23: 'Créer un jeu de rôle'
password.special_characters: '1 ou plusieurs caractères spéciaux requis'
roleplay.status.failure: 'Vous avez échoué'
roleplay.status.success: 'Vous avez réussi'
user.configureFields.locale: Langue
course.created: 'Le cours a été sauvegardé avec succès'
question.configureFields.do_all_questions: 'Utilisez toutes les questions'
announcements.news.start_announcement: 'Le course %course% est à nos portes!'
announcements.news.finish_announcement: 'Le course %course% est presque terminé!'
user.configureFields.dni: DNI
course_section.configureFields.section_aditional: 'Formation complémentaire'
report.announcement.time_conexion: 'Temps de connexion'
report.announcement.init_finish: 'Début et fin'
report.annnouncement.conexions: Connexions
report.annnouncement.chat_tutor: 'Chat avec le tuteur'
report.annnouncement.first_conexion: 'Première connexion'
report.annnouncement.last_conexion: 'Dernière connexion'
generic_token.assistance.success: 'Votre participation a été enregistrée avec succès'
generic_token.assistance.user_not_in_group: 'L''utilisateur n''appartient pas au groupe de session'
chat.notification.number_of_messages: 'Vous avez % de message(s) non lu(s)'
certificate.notification.available: 'Diplôme de la convocation au cours %s disponible pour téléchargement'
user_fields_fundae.title: 'Champs supplémentaires FUNDAE'
user_fields_fundae.social_security_number: 'Numéro de sécurité sociale'
user_fields_fundae.gender: Genre
user_fields_fundae.email_work: 'Courrier de travail'
user_fields_fundae.birthdate: 'Date de naissance'
user_fields_fundae.dni: DNI
user_fields_fundae.contribution_account: 'Compte de contribution'
user_fields_fundae.incapacity: Incapacité
user_fields_fundae.victim_of_terrorism: 'Victime du terrorisme'
user_fields_fundae.gender_violence: 'Victime de violence sexiste'
fundae_assistance_template.main_title: 'CONTRÔLE DE L''ASSIDUITÉ À LA FORMATION'
fundae_assistance_template.action_type: 'NOM DE L''ACTION DE FORMATION'
fundae_assistance_template.action_code: 'CODE D''ACTION'
fundae_assistance_template.group: GROUPE
fundae_assistance_template.start_at: 'DATE DE DÉBUT'
fundae_assistance_template.finish_at: 'DATE FIN'
fundae_assistance_template.main_formation_teacher: 'FORMATEUR / RESPONSABLE DE LA FORMATION'
fundae_assistance_template.session_number: 'SESSION NO.'
fundae_assistance_template.date: DATE
fundae_assistance_template.morning_afternoon: 'DEMAIN / APRÈS-MIDI'
fundae_assistance_template.signed: Signé
fundae_assistance_template.info_signed_person: 'Formateur/représentant en formation.'
fundae_assistance_template.assistance_data: 'Données sur les participants'
fundae_assistance_template.signatures: SIGNATURES
fundae_assistance_template.observations: REMARQUES
fundae_catalogs.main_page.title: 'Catalogues FUNDAE'
fundae_catalogs.user_company.label_in_plural: Entreprises
fundae_catalogs.user_company.label_in_singular: Entreprise
fundae_catalogs.user_professional_category.label_in_plural: 'Catégories professionnelles'
fundae_catalogs.user_professional_category.label_in_singular: 'Catégorie professionnelle'
fundae_catalogs.user_study_level.label_in_plural: 'Niveaux d''études'
fundae_catalogs.user_study_level.label_in_singular: 'Niveau d''études'
fundae_catalogs.user_work_center.label_in_plural: 'Lieux de travail'
fundae_catalogs.user_work_center.label_in_singular: 'Lieu de travail'
fundae_catalogs.user_work_department.label_in_plural: 'Départements de travail'
fundae_catalogs.user_work_department.label_in_singular: 'Département du travail'
fundae_catalogs.fields.state.title: État
fundae_catalogs.fields.state.active: Actif
fundae_catalogs.fields.state.inactive: Inactif
excel.userAnnouncement.sheet1.title: 'Informations générales'
excel.userAnnouncement.sheet1.colum1: 'Nombre d''itinéraires'
excel.userAnnouncement.sheet1.colum2: 'Nombre d''utilisateurs ayant des itinéraires'
excel.userAnnouncement.sheet1.colum3: 'Nombre de cours dans les filières'
excel.userAnnouncement.sheet2.title: 'Catalogue Itinéraires'
excel.userAnnouncement.sheet2.colum1: Id
excel.userAnnouncement.sheet2.colum2: 'Itinéraires par nom'
excel.userAnnouncement.sheet2.colum3: Division
excel.userAnnouncement.sheet2.colum4: Catégorie
excel.userAnnouncement.sheet2.colum5: 'Cours attribués'
excel.userAnnouncement.sheet2.colum6: 'Personnes désignées'
excel.userAnnouncement.sheet2.colum7: 'Les personnes ont complété l''itinéraire'
excel.userAnnouncement.sheet2.colum8: 'Les personnes dans le processus'
excel.userAnnouncement.sheet2.colum9: Non-débutants
excel.userAnnouncement.sheet2.colum10: 'TEMPS TOTAL ACCUMULÉ'
excel.userAnnouncement.sheet2.colum11: 'TEMPS MOYEN D''UNE PERSONNE'
excel.userAnnouncement.sheet3.title: 'Cours d''itinéraire'
excel.userAnnouncement.sheet3.colum1: Id
excel.userAnnouncement.sheet3.colum2: 'Itinéraires par nom'
excel.userAnnouncement.sheet3.colum3: 'Nom Cours'
excel.userAnnouncement.sheet3.colum4: Terminé
excel.userAnnouncement.sheet3.colum5: 'En cours'
excel.userAnnouncement.sheet3.colum6: 'Pas de démarrage'
course.message_saved: 'Cours sauvegardé'
chapter.configureFields.create_chapter: 'Créer un chapitre'
user_filter.assign_manual: 'Attribuer manuellement'
user_filter.assign_filters: 'Affectation par filtres'
user.configureFields.configureLocale: 'Paramètres linguistiques'
user.configureFields.configureLocaleAdmin: 'Configurer la langue du panneau d''administration'
user.configureFields.configureLocaleCampus: 'Définir la langue du campus'
stats.export.configsheet.title: Configuration
stats.export.configsheet.content_title: 'Rapport statistique général'
stats.export.configsheet.content_period: 'Période couverte (date de fin)'
stats.export.configsheet.content_filters: 'Filtres actifs'
stats.export.configsheet.content_period_from: De
stats.export.configsheet.content_period_to: Pour
stats.export.datasheet.title: Données
stats.export.filter.category: Catégorie
stats.export.filter.departament: Département
stats.export.filter.gender: Genre
stats.export.filter.activeUsers: Utilisateurs
stats.export.filter.activeUsers_val_yes: Actifs
stats.export.filter.activeUsers_val_no: Inactif
stats.export.filter.course_full_title: '100% du cours'
stats.export.filter.course_full_val_yes: Oui
stats.export.filter.course_full_val_no: Non
stats.export.filter.course_full_descr: '(Ne mentionner que les cours qui ont été suivis pendant la période indiquée)'
stats.export.filter.course_intime_title: 'Durée de vie du cours pendant la période'
stats.export.filter.course_intime_val_yes: Oui
stats.export.filter.course_intime_val_no: Non
stats.export.filter.course_intime_descr: '(N''inclure que les cours qui ont commencé et se sont terminés pendant la période indiquée)'
stats.export.filter.course_started_in_period_title: 'Cours commencés dans la plage de dates'
stats.export.filter.course_started_in_period_val_yes: Oui
stats.export.filter.course_started_in_period_val_no: Non
stats.export.filter.course_finished_in_period_title: 'Cours suivis dans la période de référence'
stats.export.filter.course_finished_in_period_val_yes: Oui
stats.export.filter.course_finished_in_period_val_no: Non
stats.export.filter.customFilters: Personnalisé
stats.content_allusers: 'Tous les utilisateurs'
stats.content_inactive: Inactif
stats.content_inactive_f: Inactif
itinerary.user.assign_manual: 'Attribuer manuellement'
itinerary.user.assign_filter: ' Affectation par filtres'
itinerary.user.modify_users: 'Affecter des personnes manuellement'
itinerary.user.filter_find_by: 'Recherche par'
common_areas.close: Fermer
itinerary.chart.avg_time_active: '(Total actif)'
itinerary.chart.avg_time_all: '(Total alloué)'
itinerary.courses.modify: 'Attribuer des cours'
itinerary.courses.appliedfilter: ' cours/s affichés (filtrés par'
itinerary.users.appliedfilter: ' personne(s) présentée(s) (filtrée(s) par'
itinerary.courses.available: 'Cours disponibles'
excel.userAnnouncement.sheet1.colum2b: 'Nombre d''utilisateurs uniques disposant d''itinéraires'
excel.userAnnouncement.sheet1.colum3b: 'Nombre de cours uniques dans les parcours'
common_areas.select_choice: 'Sélectionner une option'
chapter_type.24: LTI
chapter_type.description.24: LTI
lti_chapter.title: 'Chapitre LTI'
lti_chapter.add: 'Ajouter un chapitre LTI'
lti_chapter.edit: 'Modifier le chapitre LTI'
lti_chapter.identifier: 'Identifiant LTI'
lti_chapter.identifier_required: 'Identifiant LTI requis'
chapter_type.add.24: 'Ajouter un chapitre LTI'
categoryFilter.label: 'Filtres de catégorie'
categoryFilter.title: 'Filtrer les catégories'
user.configureFields.localeCampus: 'Langue du campus'
global.bulk.sheetValidation.error_tab_1: 'Le premier onglet ne s''appelle pas "Liste des formations".'
global.bulk.sheetValidation.error_tab_2: 'Le deuxième onglet ne s''appelle pas "Participants".'
stats.export.user_creation: 'Filtrer les utilisateurs par date de création'
stats.export.users_export_title: 'Statistiques sur les utilisateurs'
chapter_type.validation_course: "\nLe chapitre ne peut pas être supprimé car certains utilisateurs y ont déjà enregistré une activité."
user.configureFields.courses_stats.notstarted: 'non démarré'
course.configureFields.created_at: 'Date de création'
course.configureFields.translate: Traduire
messages.configureFields.timezone: 'Fuseau horaire'
user.email: E-mail
course.diploma.index: 'Diplômes personnalisés'
menu.stats.reports.diplomas: 'Rapports et diplômes'
itinerary.succes.download: 'Le rapport d''itinéraire est en cours de traitement et vous pouvez le retrouver dans "Rapports et diplômes"'
user.diploma.generate: 'Générer des diplômes'
filters.placeholder: 'Type de recherche'
filters.remove_all: 'Supprimer tout'
filters.add_all: 'Ajouter tout'
announcement.report_group_resume_individual: 'Résumé individuel'
announcement.report_downloaded_diploma: 'Diplôme téléchargé'
announcements.configureFields.code: 'Nom de l''appel'
task.status.review: 'En cours d''examen'
task.status.error: 'Erreur du système'
email.error.subject: 'Erreur dans %contexte% (ID : %id%) - Environnement : %appName% - Erreur dans %contexte% (ID : %id%) - Environnement : %appName%'
email.error.subject_no_id: 'Erreur dans %context% - Environment : %appName%'
email.error.title: 'Erreur d''exécution de la tâche'
email.error.environment: Environnement
email.error.context: Contexte
email.error.task_id: 'ID de la tâche'
email.error.error_details: 'Détails de l''erreur'
email.error.error_message: 'Message d''erreur'
email.error.error_line: Ligne
email.error.error_file: Archives
email.error.additional_info: 'Informations complémentaires'
email.error.regards: 'Meilleures salutations'
email.error.team: 'L''équipe de %appName%'
email.zombie.subject: 'Tâche en statut ZOMBIE dans %contexte% (ID : %id%) - Environnement : %appName% (ID : %id%) - Environnement : %appName% (ID : %id%)'
email.zombie.title: 'Notification de tâche en état ZOMBIE'
email.zombie.environment: Environnement
email.zombie.context: Contexte
email.zombie.task_id: 'ID de la tâche'
email.zombie.marked_as: 'a été marquée comme'
email.zombie.zombie_status: ZOMBIE
email.zombie.timeout_reason: 'parce qu''il a dépassé le temps d''exécution autorisé'
email.zombie.check_details: 'Veuillez consulter le panneau d''administration ou la console pour plus de détails et prendre les mesures qui s''imposent'
email.zombie.additional_info: 'Informations complémentaires'
email.zombie.regards: 'Meilleures salutations'
email.zombie.team: 'L''équipe de %appName%'
season.delete: 'Il n''est pas possible de supprimer cette saison, elle a actuellement des chapitres liés'
delete.season.chapters.users: "Impossible de supprimer la saison %seasonName%,\n Les chapitres (%ChaptesTitles%) ont une activité utilisateur"
delete.season.danger: 'La saison ne peut être éliminée'
stats.task.queued: 'Demande d''intervention collée'
itinerary.delete.confirm.validation: 'Actuellement, l''itinéraire est actif, pour procéder à l''action, vous devez désactiver l''itinéraire'
itinerary.delete.confirm.title: "Voulez-vous vraiment supprimer l'itinéraire\_?"
course.publish.message.active: 'Cours publié'
course.publish.message.unactive: 'Cours marqué comme non publié'
itinerary.delete.error: 'Impossible de supprimer l''itinéraire'
courser.chaperts.orders.succes: 'Mise à jour réussie de l''ordre du chapitre'
course.publish.message.unactive.chapters: 'Ne peut être publié car le cours est incomplet'
course.undelete.message: 'Le cours ne peut pas être supprimé parce qu''il a un contenu attribué'
user.roles.creator: Créateur
