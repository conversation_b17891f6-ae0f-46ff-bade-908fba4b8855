nps_question.text.description: 'Donnez-nous votre avis'
nps_question.nps.description: 'Évaluation des cours'
type_course.teleformacion.name: 'Apprentissage en ligne'
type_course.teleformacion.description: 'Pour les cours en ligne'
type_course.presencial.name: 'En personne'
type_course.presencial.description: 'Cours sur place'
type_course.mixto.name: Mixte
type_course.mixto.description: 'Il s''agit d''une combinaison d''apprentissage en ligne et de formation en face à face'
type_course.aula_virtual.name: 'Salle de classe virtuelle'
type_course.aula_virtual.description: 'Les cours sont dispensés par vidéoconférence'
alert_type_tutor.1.name: 'La personne appelée n''a pas accédé au cours'
alert_type_tutor.1.description: 'Une alerte sera envoyée au tuteur si la personne convoquée n''a pas accédé au cours'
alert_type_tutor.2.name: '50 % de l''appel s''est écoulé et 25 % du contenu n''a pas été complété'
alert_type_tutor.2.description: 'Une alerte sera envoyée au tuteur si 50 % de l''appel s''est écoulé et que 25 % du contenu n''a pas été complété'
alert_type_tutor.3.name: '80% de l''appel s''est écoulé et 50% du contenu n''a pas été complété'
alert_type_tutor.3.description: 'Une alerte sera envoyée au tuteur si 80 % de l''appel s''est écoulé et que 50 % du contenu n''a pas été complété'
alert_type_tutor.4.name: 'Il ne reste que quelques jours avant la fin de l''appel et le cours n''est pas terminé'
alert_type_tutor.4.description: 'Vous devez évaluer le nombre de jours considérés comme peu nombreux'
alert_type_tutor.5.name: 'La personne appelée a suivi le cours mais n''a pas répondu à l''enquête.'
alert_type_tutor.5.description: 'Si la plateforme propose des enquêtes, une alerte sera envoyée au tuteur si la personne appelée a terminé le cours mais n''a pas répondu à l''enquête'
alert_type_tutor.6.name: 'La personne a terminé le cours mais n''a pas téléchargé le diplôme'
alert_type_tutor.6.description: 'La personne convoquée a terminé le cours mais n''a pas téléchargé le diplôme.'
announcement_configuration_type.temporalizacion.name: Calendrier
announcement_configuration_type.temporalizacion.description: 'Afin de faciliter le suivi du cours, nous attribuerons une durée à chaque bloc de contenus et d''activités, ce qui nous permettra de détecter les participants qui travaillent à un rythme adéquat ou qui sont à la traîne dans le processus de formation'
announcement_configuration_type.curso_bonificado.name: 'Cours subventionnés'
announcement_configuration_type.curso_bonificado.description: 'Les cours subventionnés sont ceux qui sont organisés par la Fondation tripartite et qui sont financés par les entreprises par le biais des cotisations de sécurité sociale.'
announcement_configuration_type.chat.name: Chat
announcement_configuration_type.chat.description: 'Le chat est un outil de communication synchrone qui permet aux participants au cours d''interagir en temps réel, par le biais de messages textuels.'
announcement_configuration_type.notificaciones.name: Notifications
announcement_configuration_type.notificaciones.description: 'Les notifications sont des messages envoyés aux participants aux cours pour les informer de nouvelles ou d''événements importants.'
announcement_configuration_type.mensajeria.name: Messagerie
announcement_configuration_type.mensajeria.description: 'Un système de messagerie est un système de communication qui permet aux participants au cours d''envoyer et de recevoir des messages privés.'
announcement_configuration_type.foros.name: Forums
announcement_configuration_type.foros.description: 'Un forum est un espace de communication asynchrone qui permet aux participants au cours d''échanger des messages sur un sujet donné.'
announcement_configuration_type.diploma.name: Diplôme
announcement_configuration_type.diploma.description: 'Les diplômes sont des certificats qui sont délivrés aux participants d''un cours comme preuve d''achèvement.'
announcement_configuration_type.tutor_alerts.name: 'Activer les alertes des tuteurs'
announcement_configuration_type.tutor_alerts.description: 'Les alertes sont des messages envoyés au tuteur d''un cours pour l''informer de nouvelles ou d''événements importants.'
announcement_configuration_type.encuesta_satisfaccion.name: 'Enquête de satisfaction'
announcement_configuration_type.encuesta_satisfaccion.description: 'Les enquêtes de satisfaction sont des questionnaires envoyés aux participants pour connaître leur opinion sur le cours.'
announcement_configuration_type.finalizar_convocatoria.name: 'Le cours restera actif à la fin de l''appel'
announcement_configuration_type.finalizar_convocatoria.description: 'L''utilisateur pourra accéder au contenu du cours après la fin de celui-ci.'
announcement_configuration_type.firma_digital.name: 'Signature numérique'
announcement_configuration_type.firma_digital.description: 'Une signature numérique est nécessaire pour pouvoir signer l''inscription à un cours en présentiel.'
announcement_configuration_type.gestion_costes.name: 'Gestion des coûts'
announcement_configuration_type.gestion_costes.description: 'La gestion des coûts permet aux groupes d''indiquer le coût de l''appel.'
announcement_configuration_type.EMAIL_NOTIFICATION_ON_ANNOUNCEMENT.name: EMAIL_NOTIFICATION_ON_ANNOUNCEMENT
announcement_configuration_type.EMAIL_NOTIFICATION_ON_ANNOUNCEMENT.description: 'Activer les notifications de courrier.'
announcement_configuration_type.NOTIFICATION_ON_ANNOUNCEMENT.name: EMAIL_NOTIFICATION_ON_ANNOUNCEMENT
announcement_configuration_type.NOTIFICATION_ON_ANNOUNCEMENT.description: 'Activer les notifications normales'
announcement_configuration_type.objetivos_contenidos.name: 'Objectifs et contenu'
announcement_configuration_type.objetivos_contenidos.description: 'Les objectifs et le contenu du cours seront inclus dans le diplôme.'
announcement_configuration_type.dni.name: Dni
announcement_configuration_type.dni.description: 'Le numéro d''identification de l''étudiant figurera sur le diplôme'
announcement_configuration_type.template_excel.name: 'Modèle d''enregistrement excel'
announcement_configuration_type.template_excel.description: 'Ce modèle sera utilisé pour l''inscription des étudiants, en utilisant le code HRBP au lieu du DNI.'
announcement_configuration_type.report_zip.name: ZIP
announcement_configuration_type.report_zip.description: 'Permettre au tuteur de télécharger les rapports de groupe au format ZIP'
announcement_criteria.1.name: 'Nombre minimum de chapitres à compléter'
announcement_criteria.1.description: 'La note minimale peut être, par exemple, de 70 sur 100'
announcement_criteria.2.name: 'Exécution des tâches'
announcement_criteria.2.description: 'Contrôles d''évaluation'
announcement_criteria.3.name: 'Temps d''arrêt maximal'
announcement_criteria.3.description: 'Par exemple, l''utilisateur ne peut pas être inactif pendant plus de 10 minutes'
announcement_criteria.4.name: 'Réalisation d''activités'
announcement_criteria.4.description: 'L''utilisateur doit réaliser les activités proposées'
announcement_criteria.5.name: 'Heures de formation suivies'
announcement_criteria.5.description: 'Par exemple, si le programme comporte 20 heures, l''utilisateur doit effectuer les 20 heures'
announcement_step_creation.ANNOUNCEMENT_COURSE.description: 'Première étape de la création de l''appel'
announcement_step_creation.ANNOUNCEMENT_GENERAL_INFO.description: 'Deuxième étape de la création d''un appel, au cours de laquelle des informations générales sont fournies'
announcement_step_creation.ANNOUNCEMENT_BONUS.description: 'Cette étape dépend de l''activation ou non du bonus par le client'
announcement_step_creation.ANNOUNCEMENT_STUDENTS.description: 'C''est ici que les étudiants sont ajoutés à l''appel, et qu''ils peuvent être assignés à un groupe'
announcement_step_creation.ANNOUNCEMENT_GROUPS.description: 'Dans cette étape, les groupes d''étudiants créés dans l''étape précédente sont configurés'
announcement_step_creation.ANNOUNCEMENT_COMMUNICATION.description: 'Cette étape permet de configurer l''enquête à envoyer aux étudiants'
announcement_step_creation.ANNOUNCEMENT_SURVEY.description: 'Cette étape permet de configurer l''enquête à envoyer aux étudiants'
announcement_step_creation.ANNOUNCEMENT_CERTIFICATE.description: 'Dans cette étape, les diplômes disponibles sur la plateforme sont affichés pour que le client puisse sélectionner celui qu''il souhaite'
announcement_step_creation.ANNOUNCEMENT_ALERTS.description: 'Ces alertes sont des alertes spéciales qui informent le tuteur des événements de l''appel'
class_room_virtual.zoom.name: Zoom
class_room_virtual.zoom.description: 'Plateforme de conférence web en ligne, permettant des appels vidéo en haute définition, avec des fonctionnalités de partage de bureau, de tableau blanc, de chat, d''enregistrement de la conférence, de partage de documents, et pouvant être accessible de n''importe où puisqu''elle est disponible pour les appareils mobiles.'
class_room_virtual.clickmeeting.name: ClickMeeting
class_room_virtual.clickmeeting.description: 'La plateforme de ClickMeeting est l''une des interfaces de webinaire les plus conviviales du marché et offre de nombreuses options de personnalisation'
class_room_virtual.jitsi.name: Jitsi
class_room_virtual.jitsi.description: 'Solution Open Source pour la vidéoconférence avec des connexions cryptées et disponible pour différents systèmes d''exploitation'
class_room_virtual.plugnmeet.name: PlugnMeet
class_room_virtual.plugnmeet.description: 'Logiciel de vidéoconférence open source facile à intégrer et hautement personnalisable'
configuration_cliente_announcement.COMMUNICATION.description: 'Cela permet d''activer les communications au sein de l''appel'
configuration_cliente_announcement.CERTIFICATE.description: 'Cela permet de télécharger les diplômes au sein de l''appel'
configuration_cliente_announcement.SURVEY.description: 'Cela permet d''activer les enquêtes et de les rendre disponibles plus tard au cours de l''appel'
configuration_cliente_announcement.ALERT.description: 'Cela permet d''activer des alertes qui seront héritées dans la section des alertes du tuteur'
configuration_cliente_announcement.TEMPORALIZATION.description: 'Permet la temporalisation des chapitres d''un appel à propositions'
configuration_cliente_announcement.BONIFICATION.description: 'Bonus pour l''appel, en particulier pour l''appel de la fondation tripartite'
configuration_cliente_announcement.ACCESS_CONTENT.description: 'Permet d''accéder au contenu de l''appel après sa fin'
configuration_cliente_announcement.DIGITAL_SIGNATURE.description: 'Permet d''activer la signature numérique dans l''appel à candidatures, en particulier dans les cours en face à face'
configuration_cliente_announcement.COST.description: 'Permettre aux clients d''imputer les coûts à l''appel.'
configuration_cliente_announcement.NOTIFICATION_ACTIVATE_ANNOUNCEMENT.description: 'Notifications lors de l''activation de l''appel (courriel, notification frontale)'
configuration_cliente_announcement.CONFIGURATION_IBEROSTAR.description: 'Cette configuration est spécialement conçue pour les clients d''Iberostar, afin de ne pas affecter les flux de fonds'
configuration_cliente_announcement.REPORT.description: 'Permettre l''établissement de rapports dans le cadre d''appels à propositions'
type_course_announcement_step_creation.seleccionar_curso.name: 'Sélectionner un cours'
type_course_announcement_step_creation.seleccionar_curso.description: 'Sélectionnez le cours pour lequel l''appel doit être créé'
type_course_announcement_step_creation.convocatoria.name: 'Appel à candidatures'
type_course_announcement_step_creation.convocatoria.description: 'Informations sur l''appel à propositions'
type_course_announcement_step_creation.bonificacion.name: Bonus
type_course_announcement_step_creation.bonificacion.description: 'Informations sur l''appel à propositions'
type_course_announcement_step_creation.alumnado.name: Étudiants
type_course_announcement_step_creation.alumnado.description: 'Les étudiants sont ajoutés au cours'
type_course_announcement_step_creation.grupos.name: Groupes
type_course_announcement_step_creation.grupos.description: 'Les informations sur le groupe sont détaillées et le tuteur est également ajouté'
type_course_announcement_step_creation.comunicacion.name: Communication
type_course_announcement_step_creation.comunicacion.description: 'Cette étape dépend de l''activation ou non de la communication par le client.'
type_course_announcement_step_creation.encuesta.name: Enquête
type_course_announcement_step_creation.encuesta.description: 'Cette étape dépend de l''activation ou non de l''enquête par le client.'
type_course_announcement_step_creation.diploma.name: Diplôme
type_course_announcement_step_creation.diploma.description: 'Cette étape dépend de l''activation ou non du diplôme par le client.'
type_course_announcement_step_creation.alertas.name: Alertes
type_course_announcement_step_creation.alertas.description: 'Cela peut dépendre de la configuration du client.'
type_diploma.easylearning.name: Défaut
type_diploma.easylearning.description: 'Il s''agit du diplôme de l''entreprise cliente'
type_diploma.fundae.name: Fundae
type_diploma.fundae.description: 'Il s''agit du diplôme Fundae'
type_diploma.hobetuz.name: Hobetuz
type_diploma.hobetuz.description: 'Il s''agit du diplôme Hobetuz'
type_money.euro.name: 'L''euro'
type_money.euro.country: Espagne
type_money.dolar_estadounidense.name: 'Dollar américain'
type_money.dolar_estadounidense.country: États-Unis
section_default_front.mi_formacion.name: 'Ma formation'
section_default_front.mi_formacion.description: 'Dans la formation assignée, vous trouverez toutes les formations qui vous ont été assignées.'
section_default_front.formacion_adicional.name: 'Formation continue'
section_default_front.formacion_adicional.description: 'Dans cette section, vous trouverez tous les cours en campus ouvert.'
section_default_front.formacion_asignada.name: 'Formation assignée'
section_default_front.formacion_asignada.description: 'Dans cette section, vous trouverez tous les cours qui vous ont été attribués.'
setting.multi_idioma.name: Multi-langues
setting.multi_idioma.description: 'Il offre une interface multilingue'
setting.default_lenguage.name: 'Langue par défaut'
setting.default_lenguage.description: 'la langue par défaut de l''application'
setting.languages.name: Langue
setting.languages.description: 'Langues disponibles dans l''application'
setting.registro_libre.name: 'Enregistrement gratuit des utilisateurs'
setting.registro_libre.description: 'Enregistrement gratuit des utilisateurs'
setting.opinion_plataforma.name: 'Avis sur la plate-forme'
setting.opinion_plataforma.description: 'Avis sur la plate-forme'
setting.validacion_automatica.name: 'Validation automatique de l''enregistrement des utilisateurs'
setting.validacion_automatica.description: 'Validation automatique de l''enregistrement des utilisateurs'
setting.filtros_plataforma.name: 'Filtres sur la plateforme'
setting.filtros_plataforma.description: 'Il s''agit d''activer ou de désactiver les filtres de la plateforme.'
setting.itinearios_plataforma.name: 'Itinéraires sur la plateforme'
setting.itinearios_plataforma.description: 'Il s''agit d''activer ou de désactiver les itinéraires dans la plate-forme.'
setting.seccion_cursos.name: 'Sections de cours [FRONT]'
setting.seccion_cursos.description: 'Sections de cours [FRONT],'
setting.set_points_course.name: 'Établir des points pour le cours'
setting.set_points_course.description: 'Il permet d''attribuer des points aux cours lors de leur création ou de leur modification, en particulier pour les cours d''apprentissage en ligne.'
setting.default_points_course.name: 'Points par défaut '
setting.default_points_course.description: 'Cette valeur sera prise par défaut lors de l''attribution des notes pour l''achèvement d''un cours. Elle consiste à répartir 50 % entre les chapitres théoriques et 50 % entre les chapitres évaluatifs.'
setting.documentation_course.name: 'Informations générales'
setting.documentation_course.description: 'Activer active le champ de texte "Informations générales" lors de la création d''un cours'
setting.open_course.name: 'Campus ouverts'
setting.open_course.description: 'Activée, la possibilité d''activer/désactiver le campus ouvert apparaît.'
setting.client_id.name: 'identifiant client vimeo'
setting.client_id.description: 'Il s''agit de l''identifiant client Vimeo Gestionet.'
setting.client_secret.name: 'Client vimeo secret'
setting.client_secret.description: 'Client vimeo secret Gestionet'
setting.access_token.name: 'Jeton d''accès'
setting.access_token.description: 'Jeton d''accès Gestionet'
setting.user_id.name: 'identifiant client'
setting.user_id.description: 'Identifiant de l''utilisateur enregistré dans vimeo Gestionet'
setting.project_id.name: 'Dossier de chapitres vidéo'
setting.project_id.description: 'Il s''agit de l''identifiant où sont hébergées les ressources des chapitres vidéo.'
setting.project_id_resource_course.name: 'Kit de ressources matérielles (appel à propositions)'
setting.project_id_resource_course.description: 'Il s''agit de l''identifiant du dossier dans lequel sont stockées les vidéos relatives aux supports de cours et à l''appel.'
setting.project_id_task_course.name: 'Dossier de ressources pour les tâches'
setting.project_id_task_course.description: 'Il s''agit de l''identifiant du dossier dans lequel sont stockées les vidéos relatives aux tâches du cours et à l''appel.'
setting.project_id_video_Quiz.name: 'Dossier de ressources Videoquiz'
setting.project_id_video_Quiz.description: 'Il s''agit de l''identifiant du dossier dans lequel sont stockées les vidéos liées au jeu vidéoquiz.'
setting.project_id_Roleplay.name: 'Kit de ressources pour les jeux de rôle'
setting.project_id_Roleplay.description: 'Identifié pour les ressources de type vidéo dans le jeu de rôle'
setting.upload_sudomain.name: 'Télécharger vers le sous-domaine'
setting.upload_sudomain.description: 'Cette variable est utilisée pour télécharger des vidéos et des fichiers SCORM, vous permettant de surmonter les restrictions de 100 Mo de Cloudflare.'
setting.from_email.name: 'E-mail de l''expéditeur'
setting.from_email.description: 'Il s''agit de l''origine des courriels envoyés depuis la plateforme'
setting.from_name.name: 'Nom de l''expéditeur'
setting.from_name.description: 'Le nom de l''expéditeur affiché dans les e-mails'
setting.from_cif.name: CIF
setting.from_cif.description: 'ID fiscal de l''entreprise'
setting.email_support.name: 'Support par courriel'
setting.email_support.description: 'Ces courriels sont utilisés pour envoyer des notifications d''assistance'
setting.email_support_register.name: 'Réception du courrier électronique administrateur'
setting.email_support_register.description: 'Cet email sert à recevoir les demandes d''inscription sur la plateforme'
setting.news.name: Actualités
setting.news.description: 'Activé active le module "Actualités" sur la plateforme'
setting.foro.name: Forum
setting.foro.description: 'Activé active le module "Forum" sur la plateforme'
setting.desafios.name: Duels
setting.desafios.description: 'Activé active le module "Duels" sur la plateforme'
setting.secciones.name: Sections
setting.secciones.description: 'Les sections activées sont affichées au recto'
setting.encuestas.name: Enquêtes
setting.encuestas.description: 'Activé active le module "Enquêtes" sur la plateforme'
setting.active_cron_exports.name: 'Compilation du rapport en attente'
setting.active_cron_exports.description: 'Activé, le rapport est généré et placé dans une file d''attente, et désactivé, le téléchargement est direct.'
setting.gender_excel.name: 'Champ "Sexe" en xls'
setting.gender_excel.description: 'Activé active la colonne "Genre" dans les rapports de téléchargement xls'
setting.code.name: 'Champ "Code" en xls'
setting.code.description: 'Activé active la colonne "Code" dans les rapports de téléchargement xls'
setting.finished_chapters.name: 'Chapitres terminés'
setting.finished_chapters.description: "Activé active les «\_Chapitres terminés\_» dans les rapports de téléchargement xls"
setting.zoom_cliente_id.name: 'Zoom Customer ID'
setting.zoom_cliente_id.description: 'ID du client Zoom - nécessaire pour utiliser l''API Zoom'
setting.zoom_cliente_secret.name: 'Customer Secret de Zoom'
setting.zoom_cliente_secret.description: 'Clé client Zoom - nécessaire pour utiliser l''API zoom'
setting.zoom_account_id.name: 'ID du compte Zoom'
setting.zoom_account_id.description: 'Numéro de compte client Zoom - nécessaire pour utiliser l''API Zoom'
setting.zoom_email.name: 'Zoom Email'
setting.zoom_email.description: 'Courrier du client Zoom - nécessaire pour utiliser l''API Zoom'
setting.clickmeeting_api_key.name: 'Clé API ClickMeeting'
setting.clickmeeting_api_key.description: 'ID du client ClickMeeting - nécessaire pour utiliser l''API ClickMeeting'
setting.clikmeeting_dirbase.name: 'Répertoire de base de ClickMeeting'
setting.clikmeeting_dirbase.description: 'Adresse du serveur de ClickMeeting'
setting.clikmeeting_events_paralel.name: 'Événements parallèles ClickMeeting'
setting.clikmeeting_events_paralel.description: 'Nombre d''événements secondaires contractés'
setting.plugnmeet_serverurl.name: 'Url du serveur plugNmeet'
setting.plugnmeet_serverurl.description: 'Adresse du serveur plugNmeet'
setting.plugnmeet_api_key.name: 'Clé API plugNmeet'
setting.plugnmeet_api_key.description: 'ID du client plugNmeet'
setting.plugnmeet_secret.name: 'clé secrète de plugNmeet'
setting.plugnmeet_secret.description: 'clé client plugNmeet'
setting.plugnmeet_analyticsurl.name: 'plugNmeet URL analytics'
setting.plugnmeet_analyticsurl.description: 'Adresse du serveur plugNmeet pour l''analyse'
setting.zoom_urlreports.name: 'Zoom Report Url'
setting.zoom_urlreports.description: 'Adresse où les rapports de zoom sont stockés'
setting.plugnmeet_urlreports.name: 'plugNmeet reporting url'
setting.plugnmeet_urlreports.description: 'Adresse où sont stockés les rapports plugNmeet'
setting.clickmeeting_urlreports.name: 'URL de rapport ClickMeeting'
setting.clickmeeting_urlreports.description: 'Adresse où les rapports ClickMeeting sont stockés'
setting.library_enabled.name: 'Archives de journaux'
setting.library_enabled.description: 'Activé active le module "Bibliothèque de journaux" sur la plateforme'
setting.library_audio_local.name: 'Audio local'
setting.library_audio_local.description: 'Audio local'
setting.library_audio_path.name: 'Chemin audio'
setting.library_audio_path.description: 'Chemin audio '
setting.library_file_path.name: 'Chemin du fichier'
setting.library_file_path.description: 'Chemin du fichier'
setting.library_data_page_size.name: 'Taille de la page de données'
setting.library_data_page_size.description: 'Taille de la page de données'
setting.library_comments.name: 'Commentaires '
setting.library_comments.description: 'Commentaires '
setting.challenge_loops.name: 'Nombre de questions par duel'
setting.challenge_loops.description: 'Nombre par défaut de questions qui font partie d''un duel.'
setting.points_for_win.name: 'Points de victoire'
setting.points_for_win.description: 'Points attribués pour avoir gagné un duel'
setting.points_for_lose.name: 'Points pour la défaite'
setting.points_for_lose.description: 'Points soustraits pour avoir perdu un duel'
setting.points_fortie.name: 'Points pour les ex aequo'
setting.points_fortie.description: 'Points attribués pour avoir égalisé un duel avec des succès'
setting.points_corrects.name: 'Points pour un nul nul'
setting.points_corrects.description: 'Points attribués pour avoir égalisé un duel sans coup sûr'
setting.points_for_left.name: 'Points d''abandon'
setting.points_for_left.description: 'Points attribués pour avoir quitté le duel'
setting.total_duels.name: 'Nombre maximum de duels'
setting.total_duels.description: 'Nombre maximum de duels disponibles pour chaque personne participante.'
setting.seconds_per_question.name: 'Secondes disponibles par question'
setting.seconds_per_question.description: 'Temps exprimé en secondes, disponible pour répondre à chacune des questions.'
setting.user_dni.name: 'ID de l''utilisateur'
setting.user_dni.description: 'Activé en création/modification d''utilisateur, le champ "DNI" apparaît'
setting.edit_code.name: 'Code utilisateur'
setting.edit_code.description: 'Activé à la création/modification de l''utilisateur le champ "Code" apparaît'
setting.stats_acumulative.name: 'Statistiques cumulées'
setting.stats_acumulative.description: 'Ceci au cas où vous souhaiteriez que les statistiques soient cumulées.'
setting.maximo_fechas.name: 'Période maximale'
setting.maximo_fechas.description: 'Nombre maximum de jours autorisés pour la consultation des données'
setting.maximo_horas.name: 'Nombre maximum de demandes par heure'
setting.maximo_horas.description: 'Nombre maximum de requêtes autorisées par heure'
setting.maximo_dia.name: 'Maximum de demandes par jour'
setting.maximo_dia.description: 'Nombre maximum de demandes autorisées par jour'
setting.fundae.name: Fundae
setting.fundae.description: 'Si cette option est activée, lorsqu''un appel est publié, les utilisateurs doivent remplir tous les champs nécessaires du tableau users_extra_fundae.'
setting.margen_entrada.name: 'Marge d''entrée par défaut'
setting.margen_entrada.description: 'Marge d''entrée par défaut, utilisée dans le code QR'
setting.margen_salida.name: 'Marge de sortie par défaut'
setting.margen_salida.description: 'Marge de sortie par défaut, utilisée dans le code QR'
setting.registrar_qr.name: 'S''inscrire avec la session QR'
setting.registrar_qr.description: 'Si cette option est activée, les sessions seront enregistrées avec QR.'
setting.maximo_alumnos.name: 'Nombre maximum d''étudiants par groupe'
setting.maximo_alumnos.description: 'Nombre maximum d''étudiants par groupe'
setting.min_score.name: 'Note minimale de passage'
setting.min_score.description: 'Note minimale de passage'
setting.types_action.name: 'Types d''actions'
setting.types_action.description: 'Types d''actions'
setting.materiales_convocatoria.name: 'Permettre la création de documents dans le cadre d''un appel à contributions'
setting.materiales_convocatoria.description: 'Permettre la création de documents dans le cadre d''un appel à contributions'
setting.tareas_convocatoria.name: 'Permettre la création de tâches dans un appel à propositions'
setting.tareas_convocatoria.description: 'Permettre la création de tâches dans un appel à propositions'
setting.minimo_minutos.name: 'Temps d''arrêt minimum en minutes'
setting.minimo_minutos.description: 'Temps d''arrêt minimum en minutes, s''appliquant aux utilisateurs qui se trouvent sur la plateforme.'
setting.timezones.name: 'Fuseaux horaires autorisés dans l''appel à propositions'
setting.timezones.description: 'Fuseau horaire pouvant être configuré dans l''appel à candidatures'
catalog.1.name: 'Types de chapitres'
catalog.1.description: 'Configuration des types de chapitres qui seront disponibles sur la plateforme'
catalog.2.name: 'Types de cours'
catalog.2.description: 'Configuration des types de cours qui seront disponibles sur la plateforme'
catalog.3.name: 'Critères d''approbation'
catalog.3.description: 'Configuration des critères d''approbation, qui seront disponibles sur la plateforme'
catalog.4.name: 'Alertes sur les tuteurs'
catalog.4.description: 'Configuration des alertes pour les tuteurs, qui seront disponibles sur la plateforme'
catalog.5.name: 'Types de diplômes'
catalog.5.description: 'Configuration des types de diplômes qui seront disponibles sur la plateforme'
catalog.6.name: 'Configuration du client dans la convocation'
catalog.6.description: 'Configuration des étapes à présenter dans l''appel à propositions'
catalog.7.name: 'Types de devises'
catalog.7.description: 'Configuration des types de devises qui seront disponibles sur la plateforme'
catalog.8.name: 'Groupe de configurations'
catalog.8.description: 'Groupe de configurations qui seront disponibles sur la plate-forme'
catalog.9.name: Configurations
catalog.9.description: 'Configurations par groupe, qui seront disponibles sur la plateforme'
catalog.10.name: Entreprise
catalog.10.description: 'Entreprises des utilisateurs qui seront disponibles sur la plateforme'
catalog.11.name: 'Catégorie professionnelle'
catalog.11.description: 'Catégories professionnelles des utilisateurs, qui seront disponibles sur la plateforme'
catalog.12.name: 'Centre de travail de l''utilisateur'
catalog.12.description: 'les centres de travail des utilisateurs, qui seront disponibles sur la plateforme'
catalog.13.name: 'Département du travail de l''utilisateur'
catalog.13.description: 'Départements de travail des utilisateurs, qui seront disponibles sur la plateforme'
catalog.14.name: 'Niveau d''étude de l''utilisateur'
catalog.14.description: 'Niveaux d''étude des utilisateurs, qui seront disponibles sur la plateforme'
catalog.15.name: 'Étapes pour les différents types de cours'
catalog.15.description: 'Configuration des étapes pour les différents types de cours qui seront disponibles sur la plateforme'
catalog.16.name: 'Types de classes virtuelles'
catalog.16.description: 'Types de classes virtuelles pour les différents types de cours qui seront disponibles sur la plateforme'
catalog.17.name: 'Types d''identification'
catalog.17.description: 'Types d''identification disponibles sur la plateforme'
nps_question.text.name: Texte
nps_question.text.descripction: 'Donnez-nous votre avis'
setting.help.user.name: 'Inclure un pdf d''aide dans le menu utilisateur'
setting.help.user.description: 'Cette aide a été créée spécialement pour iberostar.'
catalog.18.name: 'Modalités des appels sur place'
catalog.18.description: 'Il s''agit d''un besoin particulier pour Iberostar'
setting.userPolicies_plataforma.name: 'Politique de protection de la vie privée'
setting.userPolicies_plataforma.description: 'Cette variable est utilisée pour activer une fenêtre modale sur le site lorsque l''utilisateur n''accepte pas la politique de confidentialité.'
setting.course.tab.person.name: 'Statistiques de cours par personne'
setting.course.tab.stats.name: "Statistiques générales du cours\n"
setting.course.tab.opinions.name: 'Avis par cours'
setting.documentation.name: Tutoriels
setting.documentation.description: 'Activé active le module « Tutoriels » sur la plateforme où vous pouvez télécharger des informations d''intérêt et les associer à des profils d''administration.'
setting.user_company.name: Entreprises
setting.user_company.description: 'Activé active le module « Entreprises » sur la plateforme vous permettant de créer ce filtre et d''être affecté à un tuteur dans l''appel.'
setting.pages.name: 'Pied de page'
setting.pages.description: 'Activer le pied de page sur le campus'
setting.lite_formation.name: 'Groupe de formation en statistiques générales'
setting.lite_formation.description: 'Groupe de formation en statistiques générales'
setting.lite_formation.formationHours.name: 'Heures de formation'
setting.lite_formation.formationHours.description: 'Heures de formation totales et heures de formation moyennes par personne'
setting.lite_formation.peopleWithCourses.name: 'Les gens avec cours'
setting.lite_formation.peopleWithCourses.description: 'Personnes en cours de formation et personnes ayant suivi au moins une formation'
setting.lite_formation.courseStartedAndFinished.name: 'Cours commencés, en cours et terminés'
setting.lite_formation.courseStartedAndFinished.description: 'Nombre de cours commencés, en cours et terminés'
setting.lite_formation.requiredCourses.name: 'Cours obligatoires'
setting.lite_formation.requiredCourses.description: 'Cours obligatoires assignés à un appel ou à un itinéraire'
setting.lite_formation.general.name: Général
setting.lite_formation.general.description: Général
setting.lite_formation.openedCourses.name: 'Cours ouverts'
setting.lite_formation.openedCourses.description: 'Cours volontaires'
setting.lite_formation.educativeStatus.name: 'Niveau d''éducation'
setting.lite_formation.educativeStatus.description: 'Statut de formation par niveaux de points'
setting.lite_formation.gamifiedPills.name: 'Pilules gamifiées'
setting.lite_formation.gamifiedPills.description: 'Nombre de chapitres gamifiés, échecs et réussites aux tests gamifiés'
setting.lite_formation.gamifiedTest.name: 'Pilules de test'
setting.lite_formation.gamifiedTest.description: 'Tests gamifiés utilisés et réussites et échecs par type de test'
setting.lite_formation.peoplePerformance.name: 'Performance des personnes'
setting.lite_formation.peoplePerformance.description: 'Performance des personnes'
setting.lite_formation.coursesByStars.name: 'Cours par score'
setting.lite_formation.coursesByStars.description: 'Classement par étoiles des cours'
setting.lite_formation.structureAndHotel.name: 'Départements et hôtels'
setting.lite_formation.structureAndHotel.description: 'Pourcentage par groupe'
setting.lite_formation.schoolFinishedAndProgress.name: 'École terminée et en cours'
setting.lite_formation.schoolFinishedAndProgress.description: 'École avec le plus de participation, cours en cours et terminés'
setting.lite_formation.coursesBySchool.name: 'Cours par école'
setting.lite_formation.coursesBySchool.description: 'Nombre de cours par catégorie'
setting.lite_formation.coursesByDepartment.name: 'Cours par département'
setting.lite_formation.coursesByDepartment.description: 'Création de cours par département'
setting.lite_formation.usersMoreActivesByCourses.name: 'Utilisateurs les plus actifs par cours'
setting.lite_formation.usersMoreActivesByCourses.description: 'Personnes les plus et les moins actives des cours terminés'
setting.lite_evolution.name: 'Groupe Evolution en Statistiques Générales'
setting.lite_evolution.description: 'Groupe Evolution en Statistiques Générales'
setting.lite_evolution.trainedPerson.name: 'Des personnes formées'
setting.lite_evolution.trainedPerson.description: 'Les personnes ayant suivi au moins un cours'
setting.lite_evolution.startedCourses.name: 'Cours commencés'
setting.lite_evolution.startedCourses.description: 'Cours commencés'
setting.lite_evolution.proccessCourses.name: 'Cours en cours'
setting.lite_evolution.proccessCourses.description: 'Cours en cours'
setting.lite_evolution.finishedCourses.name: 'Cours terminés'
setting.lite_evolution.finishedCourses.description: 'Cours terminés'
setting.lite_evolution.segmentedHours.name: 'Segmentation horaire'
setting.lite_evolution.userNewInPlatformThanFinishedOneCourse.name: 'Nouveaux utilisateurs ayant suivi un cours'
setting.lite_evolution.userNewInPlatformThanFinishedOneCourse.description: 'Les nouveaux arrivants sur la plateforme ayant suivi au moins un cours'
setting.lite_demography.name: 'Groupe Démographie en Statistiques Générales'
setting.lite_demography.description: 'Groupe Démographie en Statistiques Générales'
setting.lite_demography.usersBySexAndAge.name: 'Utilisateurs par sexe et âge'
setting.lite_demography.usersBySexAndAge.description: 'Utilisateurs par sexe et âge'
setting.lite_demography.ageDistribution.name: 'Répartition par âge'
setting.lite_demography.ageDistribution.description: 'Répartition par âge'
setting.lite_demography.deviceDistribution.name: 'Répartition par appareil'
setting.lite_demography.deviceDistribution.description: 'Répartition par appareils'
setting.lite_demography.usersByCountries.name: 'Répartition par pays'
setting.lite_demography.usersByCountries.description: 'Répartition par pays'
setting.lite_activity.name: 'Groupe d''activité Statistiques générales'
setting.lite_activity.description: 'Groupe d''activité Statistiques générales'
setting.lite_activity.activityInfo.name: 'Informations sur l''activité'
setting.lite_activity.activityInfo.description: 'Personnes actives sur le portail, personnes enregistrées, personnes qui ont accédé au moins une fois au cours des 30 derniers jours, personnes désactivées et personnes qui ne sont jamais entrées sur la plateforme'
setting.lite_activity.accessDays.name: 'Accès pendant des jours'
setting.lite_activity.accessDays.description: 'Jours d''accès'
setting.lite_activity.platformAccessByHours.name: 'Accès par plateforme et horaires'
setting.lite_activity.platformAccessByHours.description: 'Horaires d''accès à la plateforme par jour et heure (heat map)'
setting.lite_activity.courseStartTime.name: 'Répartition par heures de début de cours'
setting.lite_activity.courseStartTime.description: 'Heures de début des cours'
setting.lite_activity.courseEndTime.name: 'Répartition par heures de cours'
setting.lite_activity.courseEndTime.description: 'Heures d''achèvement du cours (carte thermique)'
setting.lite_activity.coursesStartedVsFinished.name: 'Cours commencés par rapport aux cours terminés'
setting.lite_activity.coursesStartedVsFinished.description: 'Cours commencés vs cours terminés'
setting.lite_activity.usersMoreActivesByActivity.name: 'Utilisateurs les plus actifs'
setting.lite_activity.usersMoreActivesByActivity.description: 'Des personnes de plus en moins actives et leur temps d''utilisation sur la plateforme'
setting.lite_itinerary.name: 'Groupe d''itinéraires en statistiques générales'
setting.lite_itinerary.description: 'Groupe d''itinéraires en statistiques générales'
setting.lite_itinerary.itinerariesStartedAndFinished.name: 'Itinéraires commencés et terminés'
setting.lite_itinerary.itinerariesStartedAndFinished.description: 'Itinéraires commencés et terminés'
setting.lite_itinerary.itinerariesCompletedByCountries.name: 'Itinéraires complétés par pays'
setting.lite_itinerary.itinerariesCompletedByCountries.description: 'Itinéraires complétés par pays'
setting.survey.hide_empty_comment.name: 'Cacher les avis avec un commentaire vide'
setting.survey.hide_empty_comment.description: 'Activé masque les opinions sans commentaire'
setting.survey.show_only_ratings.name: 'Afficher uniquement la note'
setting.survey.show_only_ratings.description: 'Activé, il affiche uniquement le nombre d''étoiles sans commentaires sur le campus, qu''il y ait ou non un commentaire écrit.'
app.survey.post_nps.enabled.name: Auto-édition
app.survey.post_nps.enabled.description: 'Le commentaire activé est publié automatiquement, désactivé nécessite la validation de l''administrateur'
setting.lite_evolution.segmentedHours.description: Heures
setting.course.tab.person.description: 'Activée, la section « Personnes » est activée au niveau détail d''un cours.'
setting.course.showDeactivatedCourses.name: 'Visualisation des cours désactivés (Campus)'
setting.course.showDeactivatedCourses.description: 'Les cours activés et désactivés (en gris) sont affichés sur le campus'
catalog.19.name: 'Administrateur des traductions'
catalog.19.description: 'Traductions de l''administrateur'
setting.lenguage.platform: 'Traductions de l''administrateur'
setting.module.announcement.name: 'Appel à candidatures'
setting.module.announcement.description: 'Activé active le module "Appels" sur la plateforme'
course.diploma.index: 'Diplômes personnalisés'
setting.zip.day_available_until.name: 'Jours disponibles'
setting.zip.day_available_until.description: 'Nombre de jours disponibles avant que le zip ne soit automatiquement supprimé.'
catalog.20.name: 'Appel de champs supplémentaires'
course.diploma.filters: 'Activer des filtres supplémentaires dans le rapport sur les diplômes'
setting.lenguage.platform.description: 'Langues disponibles dans le panneau de l''administrateur'
translations_admin.title1: 'Formation assignée'
translations_admin.title2: 'Formation complémentaire'
translations_admin.title3: 'Cours assignés'
translations_admin.title4: 'Cours volontaires'
setting.course.tab.stats.description: 'Activée, la section « Statistiques » est activée au niveau détail d''un cours.'
setting.course.tab.options.description: 'Activée, la rubrique « Avis » s''active au niveau de détail d''un cours.'
course.diploma.index.description: 'Activée, la rubrique « Diplômes » est activée lors de la création/modification d''un cours'
setting.use.filter_in_ranking.name: 'Utiliser des filtres dans le classement des utilisateurs'
setting.use.filter_in_ranking.description: 'Cette option vous permet de sélectionner dans le menu les filtres des catégories auxquelles un utilisateur souhaite être comparé. Si cette option est désactivée, l''utilisateur sera comparé par défaut à tous les filtres disponibles sur la plateforme'
setting.use.include_only_first_category_name: 'Afficher uniquement la première catégorie du filtre dans les classements'
setting.use.include_only_first_category_description: 'S''il est actif, seul le premier lien de l''utilisateur est affiché. Dans le cas contraire, toutes les catégories associées sont affichées. Par exemple, si la catégorie est "pays", l''utilisateur peut être lié à la fois à l''Espagne et au Nicaragua.'
setting.email_support_error.name: 'Courrier d''assistance en cas d''erreur'
setting.email_support_error.description: 'Mails auxquels les incidents de la plate-forme seront envoyés'
setting.export.task.slot_quantity.name: 'Nombre d''emplacements de tâches par utilisateur'
setting.export.task.slot_quantity.description: 'Nombre d''emplacements disponibles pour le traitement des tâches d''exportation par utilisateur.'
setting.export.task.long_running_type_tasks.name: 'Types de tâches à long terme'
setting.export.task.long_running_type_tasks.description: 'Liste des types de tâches considérées comme étant de longue durée pour l''exportation.'
setting.export.zip_task.slot_quantity.name: 'Nombre d''emplacements de tâches zip par utilisateur'
setting.export.zip_task.slot_quantity.description: 'Nombre d''emplacements disponibles pour le traitement des tâches de compression zip par utilisateur.'
setting.export.zip_task.long_running_type_tasks.name: 'Types de tâches de fermeture à long terme'
setting.export.zip_task.long_running_type_tasks.description: 'Liste des types de tâches de fermeture éclair considérées comme étant de longue durée.'
setting.export.task.user_pending_max_count_task.name: 'Nombre maximum de tâches en attente par utilisateur'
setting.export.task.user_pending_max_count_task.description: 'Nombre maximum de tâches en attente qu''un utilisateur peut avoir dans la file d''attente.'
setting.export.task.timeout.name: 'Délai pour les tâches'
setting.export.task.timeout.description: 'Délai maximum en secondes avant qu''une tâche d''exportation ne soit considérée comme expirée.'
setting.export.zip_task.timeout.name: 'Délai pour les tâches zip'
setting.export.zip_task.timeout.description: 'Délai maximum en secondes avant qu''une tâche de compression de zip ne soit considérée comme expirée.'
setting.export.task.timeout_seconds.name: 'Délai d''attente pour les tâches dans l''état TIMEOUT'
setting.export.task.timeout_seconds.description: 'Durée maximale en secondes après laquelle une tâche en statut TIMEOUT n''est plus considérée comme étant en cours d''exécution.'
type_diploma.novomatic.name: Novomatic
type_diploma.novomatic.description: 'C''est le diplôme personnalisé de Novomatic'
app.announcement.managers.sharing.name: 'Permettre la création de tâches dans un appel à propositions'
app.announcement.managers.sharing.description: 'Permettre la création de tâches dans un appel à propositions'
