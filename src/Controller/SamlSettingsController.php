<?php

namespace App\Controller;

use App\Admin\Traits\SerializerTrait;
use App\Admin\Traits\VueAppDefaultConfiguration;
use App\Entity\FilterCategory;
use App\Entity\Setting;
use App\Entity\SettingGroup;
use App\Entity\User;
use App\Entity\UserExtra;
use App\Repository\SettingRepository;
use App\Saml\Saml2Service;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use EasyCorp\Bundle\EasyAdminBundle\Config\KeyValueStore;
use EasyCorp\Bundle\EasyAdminBundle\Provider\AdminContextProvider;
use FOS\RestBundle\Controller\Annotations as Rest;
use Lexik\Bundle\JWTAuthenticationBundle\Services\JWTManager;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

/**
 * @Route("/admin/settings-saml")
 * @IsGranted("ROLE_SUPER_ADMIN")
 */
class SamlSettingsController extends AbstractController
{
    const ALLOWED_USER_FIELDS = [
        "email", "firstName", "lastName", "code", "teamManagerEmail", "locale", "timezone"
    ];

    const ALLOWED_USER_EXTRA_FIELDS = [
        "country", "gender", "birthdate"
    ];

    use VueAppDefaultConfiguration;
    use SerializerTrait;

    private ParameterBagInterface $params;
    private AdminContextProvider $context;
    private SettingsService $settings;
    private EntityManagerInterface $em;
    private JWTManager $JWTManager;
    private Saml2Service $saml2Service;

    public function __construct(ParameterBagInterface $params, AdminContextProvider $context, SettingsService $settings, EntityManagerInterface $em, JWTManager $JWTManager, Saml2Service $saml2Service)
    {
        $this->params = $params;
        $this->context = $context;
        $this->settings = $settings;
        $this->em = $em;
        $this->JWTManager = $JWTManager;
        $this->saml2Service = $saml2Service;
    }

    private function getSettingGroup(): SettingGroup
    {
        $settingGroup = $this->em->getRepository(SettingGroup::class)->findOneBy([
            'title' => 'SAML'
        ]);
        if (!$settingGroup)
        {
            $settingGroup = new SettingGroup();
            $settingGroup->setTitle('SAML')->setSort(35);
            $this->em->persist($settingGroup);
            $this->em->flush();
        }
        return $settingGroup;
    }

    private function validateBasicConfiguration(SettingRepository $settingRepository)
    {
        $settingGroup = $this->getSettingGroup();
        // Basic configuration
        $samlEnabled = $settingRepository->findOneBy(['code' => 'saml.enabled']);
        if (!$samlEnabled)
        {
            $samlEnabled = new Setting();
            $samlEnabled->setCode('saml.enabled')
                ->setName('Habilitar SAML')
                ->setType('bool')
                ->setSort(7)
                ->setValue($this->params->get('saml.enabled'))
                ->setSettingGroup($settingGroup);
            $this->em->persist($samlEnabled);
            $this->em->flush();
        }

        $samlDestination = $settingRepository->findOneBy(['code' => 'saml.destination']);
        if (!$samlDestination)
        {
            $samlDestination = new Setting();
            $samlDestination->setCode('saml.destination')
                ->setName('URL Login SSO SAML')
                ->setType('text')
                ->setSort(8)
                ->setValue($this->params->get('saml.destination'))
                ->setSettingGroup($settingGroup);
            $this->em->persist($samlDestination);
            $this->em->flush();
        }

        $samlIssuer = $settingRepository->findOneBy(['code' => 'saml.issuer']);
        if (!$samlIssuer)
        {
            $samlIssuer = new Setting();
            $samlIssuer->setCode('saml.issuer')
                ->setName('Quien genera la solicitud')
                ->setType('text')
                ->setSort(9)
                ->setValue($this->params->get('saml.issuer'))
                ->setSettingGroup($settingGroup);
            $this->em->persist($samlIssuer);
            $this->em->flush();
        }

        $samlUrlLogout = $settingRepository->findOneBy(['code' => 'saml.logout_url']);
        if (!$samlUrlLogout) {
            $samlUrlLogout = new Setting();
            $samlUrlLogout->setCode('saml.logout_url')
                ->setName('URL Logout, si esta vacía no se hace')
                ->setType('text')
                ->setSort(10)
                ->setValue('')
                ->setSettingGroup($settingGroup);
            $this->em->persist($samlUrlLogout);
            $this->em->flush();
        }

        // Find required configurations in database
        $userRequiredAttributes = $settingRepository->findOneBy(['code' => 'saml.user.required_attributes']);
        if (!$userRequiredAttributes)
        {
            $userRequiredAttributes = new Setting();
            $userRequiredAttributes->setCode('saml.user.required_attributes')
                ->setName('Atributos requeridos para creacion del usuario')
                ->setType('json')
                ->setSort(10)
                ->setValue(json_encode($this->params->get('saml.user.required_attributes')))
                ->setSettingGroup($settingGroup)
            ;
            $this->em->persist($userRequiredAttributes);
            $this->em->flush();
        }

        $filterConfiguration = $settingRepository->findOneBy(['code' => 'saml.user.filters']);
        if (!$filterConfiguration)
        {
            $filterConfiguration = new Setting();
            $filterConfiguration->setCode('saml.user.filters')
                ->setName('Relacion atributos y categorias')
                ->setType('json')
                ->setSort(11)
                ->setValue(json_encode([]))
                ->setSettingGroup($settingGroup);
            $this->em->persist($filterConfiguration);
            $this->em->flush();
        }
    }

    private function getUserRoles()
    {
        $userRoles = User::ROLES;
        unset($userRoles[User::ROLE_SUPER_ADMIN]);
        unset($userRoles[User::ROLE_USER]);
        return $userRoles;
    }

    /**
     * @Route("/index", name="admin_saml_settings")
     * @return Response
     */
    public function index(SettingRepository $settingRepository)
    {
        $this->validateBasicConfiguration($settingRepository);
        $keyValueStore = KeyValueStore::new([]);

        $schemeAndHttpsHost = $this->context->getContext()->getRequest()->getSchemeAndHttpHost();

        $keyValueStore = $this->configureAppResponseParameters(
            $keyValueStore,
            $this->settings,
            $this->context,
            $this->JWTManager,
            [],
            [
                'categories' => $this->em->getRepository(FilterCategory::class)
                    ->createQueryBuilder('cat')
                    ->select('cat.id', 'cat.name')
                    ->getQuery()
                    ->getResult(),
                'userProperties' => self::ALLOWED_USER_FIELDS,
                'userExtraProperties' =>self::ALLOWED_USER_EXTRA_FIELDS,
                'adminACSUrl' => $schemeAndHttpsHost . '/saml2/consume',
                'campusACSUrl' => $schemeAndHttpsHost . '/saml2/consume/campus',
                'logoutAcsUrl' => $schemeAndHttpsHost . '/saml2/logout',
                'userRoles' => $this->getUserRoles()
            ]
        );
        return $this->render(
            'admin/settings/saml_settings.html.twig',
            $keyValueStore->all()
        );
    }

    /**
     * @Rest\Post("/enable")
     * @param Request $request
     * @param SettingRepository $settingRepository
     * @return Response
     */
    public function enableSaml(Request $request, SettingRepository $settingRepository): Response
    {
        $content = json_decode($request->getContent(), true);
        $enabled = $content['enable'] ?? false;
        $saml = $settingRepository->findOneBy(['code' => 'saml.enabled']);
        if (!$saml)
        {
            $saml = new Setting();
            $saml->setCode('saml.enabled')
                ->setName('Habilitar SAML')
                ->setType('bool')
                ->setSettingGroup($this->getSettingGroup())
                ->setSort(1)
                ;
        }
        $saml->setValue($enabled);
        $this->em->persist($saml);
        $this->em->flush();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $this->params->get('saml.user.required_attributes')
        ]);
    }

    /**
     * @Rest\Get("/users")
     * @return Response
     */
    public function userConfigurations(): Response
    {
        $requiredAttributes = $this->settings->getSetting('saml.user.required_attributes');
        if (!is_array($requiredAttributes)) $requiredAttributes = $requiredAttributes ? json_decode($requiredAttributes, true) : [];

        $optionalAttributes = $this->settings->getSetting('saml.user.optional_attributes');
        if (!is_array($optionalAttributes)) $optionalAttributes = $optionalAttributes ? json_decode($optionalAttributes, true) : [];

        $roleValues = $this->settings->get('saml.user.role.roles') ?? [];
        if (!is_array($roleValues)) $roleValues = json_decode($roleValues, true);
        foreach ($this->getUserRoles() as $key => $name)
        {
            if (!array_key_exists($key, $roleValues)) $roleValues[$key] = [];
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'requiredAttributes' => $requiredAttributes,
                'optionalAttributes' => [],
                'userExtraAttributes' => $optionalAttributes['userExtraAttributes'] ?? [],
                'mainProperty' => $this->settings->get('saml.user.primaryKey') ?? '',
                'roles' => [
                    'enabled' => $this->settings->get('saml.user.role.enable'),
                    'attribute' => $this->settings->get('saml.user.role.attribute') ?? '',
                    'roles' => $roleValues,
                    'extra' => []
                ]
            ]
        ]);
    }

    /**
     * @Rest\Get("/basic-configuration")
     * @return Response
     */
    public function basicConfiguration(Request $request): Response
    {
        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'basicConfiguration' => [
                    'enabled' => $this->settings->get('saml.enabled') === true,
                    'destination' => $this->settings->get('saml.destination') ?? '',
                    'issuer' => $this->settings->get('saml.issuer') ?? '',
                    'logout_url' => $this->settings->get('saml.logout_url') ?? '',
                ],
                'signatureValidation' => [
                    'enabled' => $this->settings->get('saml.validate_signature'),
                    'certGenerated' => file_exists($this->saml2Service->getSigningCertificatePath())
                ]
            ]
        ]);
    }

    /**
     * @Rest\Get("/filters")
     * @return Response
     */
    public function filterConfigurations(): Response
    {
        $filterAttributes = $this->settings->getSetting('saml.user.filters');
        if (!is_array($filterAttributes)) $filterAttributes = $filterAttributes ? json_decode($filterAttributes) : [];
        $categories = $this->em->getRepository(FilterCategory::class)->findAll();
        $categoriesIndexed = [];
        foreach ($categories as $category)
        {
            $categoriesIndexed['id_' . $category->getId()] = [
                'id' => $category->getId(),
                'name' => $category->getName()
            ];
        }
        $attributes = [];
        foreach ($filterAttributes as $key => $v)
        {
            $attributeCategories = [];
            foreach ($v as $catId) $attributeCategories[] = $categoriesIndexed['id_' . $catId];
            $attributes[] = [
                'name' => $key,
                'categories' => $attributeCategories
            ];
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $attributes
        ]);
    }

    /**
     * @Rest\Post("/save-configuration")
     * @param Request $request
     * @param SettingRepository $settingRepository
     * @return Response
     */
    public function saveConfiguration(Request $request, SettingRepository $settingRepository): Response
    {
        $content = json_decode($request->getContent(), true);
        $code = $content['code'] ?? null;
        if (empty($code)) return $this->sendResponse([
            'status' => Response::HTTP_ACCEPTED,
            'error' => true,
            'data' => 'Code required'
        ]);
        $setting = $settingRepository->findOneBy(['code' => $code]);
        $value = $content['value'] ?? null;
        $settingGroup = $this->getSettingGroup();
        if (!$setting)
        {
            $name = $content['name'] ?? $code;
            $type = $content['type'] ?? 'text';
            $setting = new Setting();
            $setting->setCode($code)
                ->setName($name)
                ->setType($type)
                ->setSort(1)
                ->setSettingGroup($settingGroup)
            ;
        }

        switch ($code)
        {
            case 'saml.user.required_attributes':
                $mainProperty = $content['mainProperty'] ?? 'email';
                $mainPropertySetting = $settingRepository->findOneBy(['code' => 'saml.user.primaryKey']);
                if (!$mainPropertySetting)
                {
                    $mainPropertySetting = new Setting();
                    $mainPropertySetting->setCode('saml.user.primaryKey')
                        ->setName('Propiedad principal usuario')
                        ->setDescription('Propiedad usada para buscar al usuario, al no existir el usuario lo crea')
                        ->setSort(30)
                        ->setType('text')
                        ->setSettingGroup($settingGroup)
                    ;
                }
                $mainPropertySetting->setValue($mainProperty);
                $this->em->persist($mainPropertySetting);

                if (is_array($value)) {
                    foreach ($this->params->get('saml.user.default_and_required') as $property => $attribute)
                    {
                        $value[$property] = $attribute;
                    }
                }
                break;
            case 'saml.user.optional_attributes':
                if (array_key_exists('userExtraAttributes', $content))
                {
                    // Key sent, save attributes or remove all attributes
                    $userExtraAttributes = $content['userExtraAttributes'] ?? [];
                    foreach ($userExtraAttributes as $property => $attribute)
                    {
                        $value['userExtraAttributes'][$property] = $attribute;
                    }
                }
                break;
            case 'saml.user.filters':
                if (is_array($value))
                {
                    // Clean filters
                    $temp = [];
                    foreach ($value as $attribute)
                    {
                        $catIds = [];
                        foreach ($attribute['categories'] as $category) $catIds[] = $category['id'];
                        $temp[$attribute['name']] = $catIds;
                    }
                    $value = $temp;
                }
                break;
        }

        if (is_array($value)) $setting->setValue(json_encode($value));
        else $setting->setValue($value);
        $this->em->persist($setting);
        $this->em->flush();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false
        ]);
    }

    /**
     * @Rest\Post("/save-basic-config")
     * @param Request $request
     * @param SettingRepository $settingRepository
     * @return Response
     */
    public function saveBasicInfo(Request $request, SettingRepository $settingRepository): Response
    {
        $content = json_decode($request->getContent(), true);
        $destination = $content['destination'] ?? '';
        $issuer = $content['issuer'] ?? '';
        $logout_url = $content['logout_url'] ?? '';

        $destinationConfig = $settingRepository->findOneBy(['code' => 'saml.destination']);
        if ($destinationConfig)
        {
            $destinationConfig->setValue($destination);
            $this->em->persist($destinationConfig);
        }

        $issuerConfig = $settingRepository->findOneBy(['code' => 'saml.issuer']);
        if ($issuerConfig)
        {
            $issuerConfig->setValue($issuer);
            $this->em->persist($issuerConfig);
        }

        $logoutUrlConfig = $settingRepository->findOneBy(['code' => 'saml.logout_url']);
        if ($logoutUrlConfig)
        {
            $logoutUrlConfig->setValue($logout_url);
            $this->em->persist($logoutUrlConfig);
        }
        $this->em->flush();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false
        ]);
    }

    /**
     * @Rest\Post("/save-role-configuration")
     * @param Request $request
     * @param SettingRepository $settingRepository
     * @return Response
     */
    public function saveUserRolesConfiguration(Request $request, SettingRepository $settingRepository): Response
    {
        $content = json_decode($request->getContent(), true);
        $enabled = $content['enabled'] ?? false;

        $settingGroup = $this->getSettingGroup();
        $enabledSetting = $settingRepository->findOneBy(['code' => 'saml.user.role.enable']);
        if (!$enabledSetting)
        {
            $enabledSetting = new Setting();
            $enabledSetting->setCode('saml.user.role.enable')
                ->setName('Habilitar lectura de rol')
                ->setDescription('Habilitar lectura de rol desde atributo')
                ->setSort(30)
                ->setSettingGroup($settingGroup)
                ->setType('bool')
            ;
        }
        $enabledSetting->setValue($enabled ? 'true' : 'false');
        $this->em->persist($enabledSetting);

        $attribute = $content['attribute'] ?? '';
        if ($enabled && empty($attribute)) return $this->sendResponse([
            'status' => Response::HTTP_ACCEPTED,
            'error' => true,
            'data' => 'Al habilitar lectura de rol desde atributo, es requerido declarar cual es el atributo'
        ]);
        $attributeSetting = $settingRepository->findOneBy(['code' => 'saml.user.role.attribute']);
        if (!$attributeSetting)
        {
            $attributeSetting = new Setting();
            $attributeSetting->setCode('saml.user.role.attribute')
                ->setName('Atributo role')
                ->setDescription('Atributo del que se va a obtener el rol')
                ->setSort(31)
                ->setType('text')
                ->setSettingGroup($settingGroup)
            ;
        }
        $attributeSetting->setValue($attribute);
        $this->em->persist($attributeSetting);

        $roles = $content['roles'] ?? [];
        $rolesSetting = $settingRepository->findOneBy(['code' => 'saml.user.role.roles']);
        if (!$rolesSetting)
        {
            $rolesSetting = new Setting();
            $rolesSetting->setCode('saml.user.role.roles')
                ->setName('Lista de roles')
                ->setDescription('Valores del atributo con relación a un rol')
                ->setSort(32)
                ->setType('json')
                ->setSettingGroup($settingGroup)
            ;
        }
        $rolesSetting->setValue(json_encode($roles));
        $this->em->persist($rolesSetting);

        $this->em->flush();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false
        ]);
    }

    /**
     * @Route("/service-metadata")
     * @param Request $request
     * @return Response
     */
    public function downloadMetadata(Request $request): Response
    {
        $content = $this->saml2Service->generateEntityDescriptor($request);
        $response = new Response($content);
        $response->headers->set('Content-Type', 'text/xml');
        return $response;
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * @Rest\Post("/upload-idp-metadata")
     * @param Request $request
     * @return Response
     */
    public function uploadIdpMetadata(Request $request): Response
    {
        try {
            $result = $this->saml2Service->handleIdpMetadata($request);
            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => $result
            ]);
        } catch (\RuntimeException $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => $e->getMessage()
            ]);
        }
    }

    /**
     * @Rest\Post("/activate-signature-validation")
     * @param Request $request
     * @param SettingRepository $settingRepository
     * @return Response
     */
    public function activateSignatureValidation(Request $request, SettingRepository $settingRepository): Response
    {
        $content = json_decode($request->getContent(), true);
        $enabled = $content['enabled'] ?? false;

        $setting = $settingRepository->findOneBy(['code' => 'saml.validate_signature']);
        if (!$setting)
        {
            $setting = new Setting();
            $setting->setCode('saml.validate_signature')
                ->setName('Habilitar verificación de firmas')
                ->setType('bool')
                ->setSettingGroup($this->getSettingGroup())
                ->setSort(1)
            ;
        }
        $setting->setValue($enabled);
        $this->em->persist($setting);
        $this->em->flush();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false
        ]);
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * @Rest\Post("/generate-signing-certificate")
     * @param Request $request
     * @return Response
     */
    public function generateCertificates(Request $request): Response
    {
        try {
            $this->saml2Service->generateSaml2Certificates();
            $certificate = $this->saml2Service->getSigningCertificate();
            if ($certificate === false)
                return $this->sendResponse([
                    'status' => Response::HTTP_ACCEPTED,
                    'error' => true,
                    'data' => 'Cannot open file'
                ]);

            return $this->sendResponse([
                'status' => Response::HTTP_CREATED,
                'error' => false
            ]);
        } catch (\RuntimeException $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => true,
                'data' => $e->getMessage()
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage()
            ]);
        }
    }

    /**
     * @Rest\Get("/download-signing-certificate")
     * @return Response
     */
    public function downloadCertificate(): Response
    {
        $certificate = $this->saml2Service->getSigningCertificate();
        if ($certificate === false)
            return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => 'Cannot open file'
            ]);

        $response = new Response($certificate);
        $response->headers->set('Content-Type', 'application/x-x509-user-cert');
        $response->headers->set('Content-Disposition', 'attachment; filename="easylearning-saml2-signing-certificate.crt"');
        $response->headers->set('Content-Length', strlen($certificate));

        return $response;
    }

    /**
     * @Rest\Post("/upload-idp-certificate")
     * @param Request $request
     * @return Response
     */
    public function uploadIdpCertificate(Request $request)
    {
        try {
            $result = $this->saml2Service->saveIdpUploadedCertificate($request);
            if ($result === false) return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => 'Cannot open file'
            ]);
            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage()
            ]);
        }
    }
}
