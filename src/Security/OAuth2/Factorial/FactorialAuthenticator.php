<?php

declare(strict_types=1);

namespace App\Security\OAuth2\Factorial;

use App\Admin\Traits\AuthenticationLoggerTrait;
use App\Entity\User;
use App\Entity\UserToken;
use App\Security\Integrations\Clients\VicioSchoolClient;
use App\Security\Integrations\Exceptions\IntegrationException;
use App\Security\Integrations\Exceptions\IntegrationMappingException;
use App\Security\OAuth2\OAuth2BaseAuthenticator;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use KnpU\OAuth2ClientBundle\Client\ClientRegistry;
use League\OAuth2\Client\Token\AccessToken;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\KernelInterface;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Exception\AuthenticationException;
use Symfony\Component\Security\Http\Authenticator\Passport\Badge\UserBadge;
use Symfony\Component\Security\Http\Authenticator\Passport\SelfValidatingPassport;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Twig\Environment;

class FactorialAuthenticator extends OAuth2BaseAuthenticator
{
    use AuthenticationLoggerTrait;
    private UserPasswordHasherInterface $userPasswordHasher;
    private LoggerInterface $logger;
    private HttpClientInterface $httpClient;
    private Environment $twig;
    private string $userLogDirectory;
    private VicioSchoolClient $vicioSchoolClient;

    public function __construct(
        ClientRegistry $clientRegistry,
        EntityManagerInterface $em,
        SettingsService $settingsService,
        UserPasswordHasherInterface $userPasswordHasher,
        HttpClientInterface $httpClient,
        Environment $twig,
        LoggerInterface $logger,
        KernelInterface $kernel,
        VicioSchoolClient $vicioSchoolClient
    ) {
        parent::__construct($clientRegistry, $em, $settingsService);
        $this->userPasswordHasher = $userPasswordHasher;
        $this->httpClient = $httpClient;
        $this->twig = $twig;
        $this->logger = $logger;
        $this->userLogDirectory = $kernel->getProjectDir().DIRECTORY_SEPARATOR.'var'.DIRECTORY_SEPARATOR.'log'.DIRECTORY_SEPARATOR.'authentication';
        if (!file_exists($this->userLogDirectory)) {
            mkdir($this->userLogDirectory, 0777, true);
        }
        $this->vicioSchoolClient = $vicioSchoolClient;
    }

    public function supports(Request $request): ?bool
    {
        if (true !== $this->settingsService->get('oauth2.enabled')) {
            return false;
        }

        return 'oauth2-authenticate' === $request->attributes->get('_route') && 'factorial' === $this->settingsService->get('oauth2.provider');
    }

    private function storeTokens(User $user, AccessToken $accessToken)
    {
        $token = new UserToken();
        $token->setToken($accessToken->getToken())
            ->setUser($user)
            ->setValidUntil((new \DateTimeImmutable())->modify('+1 hour'))
            ->setType(UserToken::TYPE_OAUTH2_TOKEN)
        ;

        $refreshToken = new UserToken();
        $refreshToken->setToken($accessToken->getRefreshToken())
            ->setValidUntil((new \DateTimeImmutable())->modify('+1 week'))
            ->setUser($user)
            ->setType(UserToken::TYPE_OAUTH2_REFRESH_TOKEN)
        ;

        $this->em->persist($token);
        $this->em->persist($refreshToken);
        $this->em->flush();
    }

    public function authenticate(Request $request): SelfValidatingPassport
    {
        $log = "-- FactorialAuthenticator --\n";
        $log .= '-- '.(new \DateTimeImmutable())->format('c')." --\n";

        $accessToken = null;
        try {
            $client = $this->clientRegistry->getClient('factorial');
            $accessToken = $this->fetchAccessToken($client);

            $user = $this->vicioSchoolClient->getUser(['oauth2' => $accessToken->getToken()]);
        } catch (IntegrationException|IntegrationMappingException $e) {
            $log .= 'Authentication Failed: Mapping error -> '.$e->getMessage()."\n";
            $this->writeContentToLogFile($this->userLogDirectory, $log);
            throw new AuthenticationException($e->getMessage(), $e->getCode(), $e);
        } catch (\Exception $exception) {
            $log .= "Authentication Failed: Exception raised when requesting user information\n";
            if ($accessToken) {
                $log .= 'Token: '.$accessToken->getToken()."\n";
            } else {
                $log .= "No user token available\n";
            }
            $log .= $exception->getMessage()."\n";
            $log .= $exception->getTraceAsString()."\n";
            $this->writeContentToLogFile($this->userLogDirectory, $log);
            throw new AuthenticationException($exception->getMessage());
        }

        $this->storeTokens($user, $accessToken);

        return new SelfValidatingPassport(
            new UserBadge($user->getCode(), function () use ($user, $log) {
                if (!empty($managerId) && ($manager = $this->em->getRepository(User::class)->findOneBy(['code' => $managerId]))) {
                    $user->setTeamManager($manager);
                }

                if (!$user->getIsActive()) {
                    $log .= 'User: '.$user->getEmail().'|'.$user->getCode()." is not active\n";
                    $this->writeContentToLogFile($this->userLogDirectory, $log);
                    throw new AuthenticationException('User is not active. Request information from your company provider');
                }

                $log .= 'Authentication Success: '.$user->getEmail().'|'.$user->getCode()."\n";
                $this->writeContentToLogFile($this->userLogDirectory, $log);

                return $user;
            })
        );
    }

    public function onAuthenticationSuccess(Request $request, TokenInterface $token, string $firewallName): ?Response
    {
        return null; // Let tha request continue to be handled by the controller
    }

    public function onAuthenticationFailure(Request $request, AuthenticationException $exception): ?Response
    {
        $log = "-- Authentication Failed --\n";
        $log .= $exception->getMessage()."\n";
        $log .= "-- --\n";
        $this->writeContentToLogFile($this->userLogDirectory, $exception->getMessage());
        $content = $this->twig->render('security/unauthorized.html.twig', [
            'message' => $exception->getMessage(),
        ]);

        return new Response($content, Response::HTTP_FORBIDDEN);
    }
}
