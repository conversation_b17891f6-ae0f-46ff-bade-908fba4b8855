<?php

namespace App\Modules\TranslationsAdmin\Controller;

use App\Entity\TranslationsAdmin;
use App\Repository\TranslationsAdminRepository;

use App\Modules\TranslationsAdmin\Services\TranslationsAdminCrudService;

use App\Modules\Common\Controller\BaseVueController;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Config\KeyValueStore;
use EasyCorp\Bundle\EasyAdminBundle\Provider\AdminContextProvider;
use FOS\RestBundle\Controller\Annotations as Rest;
use Lexik\Bundle\JWTAuthenticationBundle\Services\JWTManager;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Contracts\Translation\TranslatorInterface;

/**
 * @Route("/admin/")
 */
class TranslationsAdminController extends BaseVueController 
{
    private AdminContextProvider $context;
    private JWTManager $JWTManager;
    private TranslationsAdminCrudService $translationsAdminCrudService;
    private TranslationsAdminRepository $translationsAdminRepository;

    public function __construct(
        AdminContextProvider $context,
        JWTManager $JWTManager,
        SettingsService $settings,
        EntityManagerInterface $em,
        LoggerInterface $logger,
        RequestStack $requestStack,
        TranslationsAdminCrudService $translationsAdminCrudService,
        TranslatorInterface $translator,
        TranslationsAdminRepository $translationsAdminRepository
    ) {

        parent::__construct($settings, $em, $logger, $requestStack, $translator);

        $this->context = $context;
        $this->JWTManager = $JWTManager;
        $this->translationsAdminCrudService = $translationsAdminCrudService;
        $this->translationsAdminRepository = $translationsAdminRepository;
    }

    public static function getEntityFqcn(): string
    {
        return TranslationsAdmin::class;
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud->overrideTemplate('crud/index', 'admin/translationsAdmin/app.html.twig');
    }

    public function configureResponseParameters(KeyValueStore $responseParameters): KeyValueStore
    {
        if (Crud::PAGE_INDEX ===  $responseParameters->get('pageName')) {
            $this->configureAppResponseParameters(
                $responseParameters,
                $this->settings,
                $this->context,
                $this->JWTManager,
                [],
            );
        }
        return $responseParameters;
    }

    /**
     * @Rest\Get("translationsAdmin/all")
     * @return Response
     */
    public function getAllTranslationsAdmin()
    {
        return $this->executeSafe(function () {
            $translationsAdmins = $this->em->getRepository(TranslationsAdmin::class)->findAll();            
            $data = [];
            foreach ($translationsAdmins as $translationsAdmin) {
                $data[] = $this->translationsAdminCrudService->formatTranslationsAdminStructure($translationsAdmin);
            }
            
            return  [
                'translationsAdmin' => $data,
                'languages' => $this->settings->get('app.languages'),  
            ];
        });
    }
}
