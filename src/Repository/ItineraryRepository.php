<?php

declare(strict_types=1);

namespace App\Repository;

use App\Admin\Traits\StatsQueryFiltersTrait;
use App\Admin\Traits\UserManagerTrait;
use App\Entity\Course;
use App\Entity\Filter;
use App\Entity\FilterCategory;
use App\Entity\Itinerary;
use App\Entity\ItineraryCourse;
use App\Entity\ItineraryUser;
use App\Entity\User;
use App\Entity\UserCourse;
use App\Entity\UserCourseChapter;
use App\Entity\UserExtra;
use App\Utils\TimeUtils;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\DBAL\Cache\QueryCacheProfile;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Query;
use Doctrine\ORM\Query\Expr;
use Doctrine\ORM\Query\ResultSetMapping;
use Doctrine\ORM\QueryBuilder;
use Doctrine\ORM\Tools\Pagination\Paginator;
use Doctrine\Persistence\ManagerRegistry;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use Psr\Log\LoggerInterface;

/**
 * @extends ServiceEntityRepository<Itinerary>
 *
 * @method Itinerary|null find($id, $lockMode = null, $lockVersion = null)
 * @method Itinerary|null findOneBy(array $criteria, array $orderBy = null)
 * @method Itinerary[]    findAll()
 * @method Itinerary[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ItineraryRepository extends ServiceEntityRepository
{
    use StatsQueryFiltersTrait;
    use UserManagerTrait;

    private LoggerInterface $logger;

    public function __construct(ManagerRegistry $registry, LoggerInterface $logger)
    {
        parent::__construct($registry, Itinerary::class);
        $this->logger = $logger;
    }

    public function paginate($dql, $page = 1, $limit = 3)
    {
        $paginator = new Paginator($dql, false);
        $paginator->setUseOutputWalkers(false);

        $paginator->getQuery()
            ->setFirstResult($limit * ($page - 1)) // Offset
            ->setMaxResults($limit); // Limit

        return $paginator;
    }

    public function add(Itinerary $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(Itinerary $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function addCourse(Itinerary $itinerary, Course $course, int $position = 0)
    {
        $itineraryCourse = new ItineraryCourse();
        $itineraryCourse->setCourse($course);
        $itineraryCourse->setItinerary($itinerary);
        $itineraryCourse->setPosition($position);
        $itinerary->addItineraryCourse($itineraryCourse);
        $this->_em->flush();
    }

    public function removeCourse(Itinerary $itinerary, Course $course)
    {
        $itineraryCourse = $this->_em->getRepository(ItineraryCourse::class)->findOneBy(['itinerary' => $itinerary, 'course' => $course]);
        $itinerary->removeItineraryCourse($itineraryCourse);
        $this->_em->flush();
    }

    public function getUsersFromItineraryUser(Itinerary $itinerary, $filters = [], ?User $manager = null)
    {
        $query = $this->_em->createQueryBuilder()
            ->select('u.id')
            ->from('App:User', 'u')
            ->join('u.itineraryUsers', 'iu')
            ->where('iu.itinerary = :itinerary')
            ->setParameter('itinerary', $itinerary);

        if (null !== $manager) {
            $this->getAssignedUsersByFilters($query, $manager);
        }

        $this->setQueryFilters($query, $filters);

        return $query->getQuery()->getResult(Query::HYDRATE_ARRAY);
    }

    public function getUsersIdsFromItineraryFilters(Itinerary $itinerary, ?User $manager = null)
    {
        $filtersByCategory = [];
        foreach ($itinerary->getFilters() as $filter) {
            $catId = $filter->getFilterCategory()->getId();
            $filtersByCategory[$catId][] = $filter->getId();
        }

        if (empty($filtersByCategory)) {
            return [];
        }

        $allFilterIds = [];
        foreach ($filtersByCategory as $filterIds) {
            $allFilterIds = array_merge($allFilterIds, $filterIds);
        }
        $allFilterIds = array_unique($allFilterIds);

        $qb = $this->_em->createQueryBuilder();
        $qb->select('u.id')
            ->from(User::class, 'u')
            ->join('u.filter', 'f')  // tu relación many-to-many
            ->where('u.isActive = 1');

        if (null !== $manager) {
            $this->getAssignedUsersByFilters($qb, $manager);
        }

        $qb->andWhere($qb->expr()->in('f.id', ':allFilterIds'))
            ->groupBy('u.id')
            ->having('COUNT(DISTINCT f.filterCategory) = :numCategories')
            ->setParameter('allFilterIds', $allFilterIds)
            ->setParameter('numCategories', \count($filtersByCategory));

        return $qb->getQuery()->getResult(Query::HYDRATE_ARRAY);
    }

    /**
     * Calculate completed and started courses from itinerary based on usersIds and coursesIds.
     *
     * @return float|int|mixed|string
     */
    private function calculateStartedAndCompletedCourses(array $usersIds, array $coursesId)
    {
        // if (empty($usersIds) or empty($coursesId)) return [];
        if (empty($usersIds)) {
            return [];
        }

        $resultSetMapping = new ResultSetMapping();
        $resultSetMapping->addScalarResult('id', 'id', 'integer');
        $resultSetMapping->addScalarResult('first_name', 'first_name');
        $resultSetMapping->addScalarResult('last_name', 'last_name');
        $resultSetMapping->addScalarResult('email', 'email');
        $resultSetMapping->addScalarResult('gender', 'gender');
        $resultSetMapping->addScalarResult('completed', 'completed', 'integer');
        $resultSetMapping->addScalarResult('started', 'started', 'integer');
        $resultSetMapping->addScalarResult('total_time', 'total_time', 'integer');

        //        $nativeQuery = $this->_em->createNativeQuery("
        //        SELECT
        //            u.id,
        //            u.first_name,
        //            u.last_name,
        //            u.email,
        //            ue.gender,
        //            ifnull( completed.total, 0 ) AS completed,
        //            ifnull(started.total, 0) as started
        //        FROM user u
        //        LEFT JOIN(
        //            SELECT
        //                uc.user_id, count(*) as total
        //            FROM user_course uc
        //            WHERE uc.user_id IN (:USERS_ID)
        //              AND uc.course_id IN (:COURSES_ID)
        //              AND uc.started_at IS NOT NULL
        //              AND uc.finished_at IS NULL
        //            GROUP BY uc.user_id
        //        ) started ON started.user_id = u.id
        //        LEFT JOIN(
        //            SELECT
        //                uc.user_id, count(*) AS total
        //            FROM user_course uc
        //            WHERE uc.user_id IN (:USERS_ID)
        //              AND uc.course_id IN (:COURSES_ID)
        //              AND uc.finished_at IS NOT NULL
        //            GROUP BY uc.user_id
        //        ) completed ON completed.user_id = u.id
        //        LEFT JOIN user_extra ue ON ue.user_id = u.id
        //        WHERE u.id in (:USERS_ID)
        //        ", $resultSetMapping);

        $nativeQuery = $this->_em->createNativeQuery('
            select
                u.id,
                u.first_name,
                u.last_name,
                u.email,
                ue.gender,
                sum(case when completed > 0 then 1 else 0 end) as completed,
                sum(case when started > 0 and completed = 0 then 1 else 0 end) as started,
                sum(ucg.total_time) as total_time
            from
                (SELECT * FROM user u WHERE u.id IN (:USERS_ID)) u
                LEFT JOIN   (
                    select
                        uct.user_id,
                        c.id,
                        uct.course_id,
                        sum(case when uct.course_id is null then 0 else 1 end) as started,
                        sum(CASE WHEN uct.finished_at IS NOT NULL THEN 1 ELSE 0 END) as completed,
                        sum(uct.time_spent) as total_time
                    from course c
                             left join (
                                select
                                    uc.id,
                                    uc.user_id,
                                    (case when c.translation_id is null then c.id else c.translation_id end) as course_id,
                                    c.translation_id,
                                    uc.started_at,
                                    uc.finished_at,
                                    uc.valued_at,
                                    ucc.time_spent 
                                from user_course uc
                                        join course c on
                                            uc.course_id = c.id
                                        and (
                                                c.id in
                                                (:COURSES_ID)
                                                or c.translation_id in
                                                (:COURSES_ID)
                                            )
                                        INNER JOIN user_course_chapter ucc ON uc.id = ucc.user_course_id
                                where
                                    uc.user_id in (:USERS_ID)
                                AND uc.announcement_id IS NULL
                        ) as uct on uct.course_id = c.id
                        
                    where c.id in (:COURSES_ID)
                    group by uct.user_id, c.id
                ) ucg on u.id = ucg.user_id
            left join user_extra ue on u.id = ue.user_id
            group by u.id
        ', $resultSetMapping);

        $nativeQuery->setParameters(['USERS_ID' => $usersIds, 'COURSES_ID' => $coursesId]);

        return $nativeQuery->getResult();
    }

    public function getCoursesTotalFromItinerary($itinerary)
    {
        $query = $this->_em->createQueryBuilder()
            ->select('count(ic.id) as total')
            ->from(ItineraryCourse::class, 'ic')
            ->andWhere('ic.itinerary = :itinerary_id')
            ->setParameter('itinerary_id', $itinerary->getId());

        $results = $query->getQuery()->getResult();

        return array_pop($results)['total'];
    }

    public function getUsersTotalFromItinerary($itinerary)
    {
        $query = $this->_em->createQueryBuilder()
            ->select('count(iu.user) as total')
            ->from(ItineraryUser::class, 'iu')
            ->andWhere('iu.itinerary = :itinerary_id')
            ->setParameter('itinerary_id', $itinerary->getId());

        $results = $query->getQuery()->getResult();

        return array_pop($results)['total'];
    }

    public function getTotalUsersIdsAssignedByHand($itinerary)
    {
        $query = $this->_em->createQueryBuilder()
            ->select('iu')
            ->from(ItineraryUser::class, 'iu')
            ->where('iu.itinerary = :itinerary_id')
            ->setParameter('itinerary_id', $itinerary->getId());

        return $query->getQuery()->getResult();
    }

    public function getTotalUsersIdsAssignedByHandAsArray($itinerary)
    {
        $query = $this->_em->createQueryBuilder()
            ->select('iu')
            ->from(ItineraryUser::class, 'iu')
            ->where('iu.itinerary = :itinerary_id')
            ->setParameter('itinerary_id', $itinerary->getId());

        $results = $query->getQuery()->getResult();

        $courses = $itinerary->getItineraryCourses();
        $coursesId = [];
        foreach ($courses as $course) {
            $coursesId[] = $course->getCourse()->getId();
        }

        $usersIds = [];

        if (!empty($results)) {
            foreach ($results as $r) {
                $usersIds[] = $r->getUser()->getId();
            }

            return $this->calculateStartedAndCompletedCourses($usersIds, $coursesId);
        }

        return [];
    }

    public function getTotalUsersAssignedByFilters($itinerary)
    {
        /*

        $query = $this->_em->createQueryBuilder()
            ->select('if')
            ->from("App\Entity\ItineraryFilter", 'itf')
            ->leftJoin("App\Entity\UserFilter","uf","uf.filter_id = itf.filter_id")
            ->where('iu.itinerary = :itinerary_id')
            ->setParameter('itinerary_id', $itinerary->getId() );

        $results = $query->getQuery()->getResult();

        */

        $conn = $this->_em->getConnection();
        $sql = 'SELECT uf.user_id
                FROM itinerary_filter itf
                LEFT JOIN user_filter uf ON itf.filter_id = uf.filter_id
                WHERE itf.itinerary_id = :ITINERARY_ID';

        $query = $conn->prepare($sql);

        $results_raw = $query->executeQuery(['ITINERARY_ID' => $itinerary->getId()]);
        $results = $results_raw->fetchAllAssociative();

        return $results;
    }

    public function getUsersWithStartedItinerary($itinerary)
    {
        $conn = $this->_em->getConnection();
        $sql = 'SELECT data.itinerary_id, data.user_id, data.course_id, data.ic_id, data.announcement_id, data.started_at, data.finished_at, data.valued_at,
            sum(case when data.valued_at IS NULL AND data.started_at IS NOT NULL then 1 else 0 end) as started
            FROM
            (   
            SELECT ic.itinerary_id, uc.user_id, uc.course_id, ic.id AS ic_id, uc.announcement_id, uc.started_at, uc.finished_at, uc.valued_at, ucc.time_spent
                FROM itinerary_course ic
                LEFT JOIN course c ON ic.course_id = c.translation_id
                INNER JOIN user_course uc ON (uc.course_id = ic.course_id OR (uc.course_id = c.id AND ic.course_id = c.translation_id ))
                        AND uc.announcement_id IS NULL
                INNER JOIN user_course_chapter ucc ON uc.id = ucc.user_course_id
                WHERE ic.itinerary_id = :ITINERARY_ID 
                GROUP BY ic.itinerary_id, uc.user_id
            ) AS data
            GROUP BY data.user_id';

        $query = $conn->prepare($sql);

        $results_raw = $query->executeQuery(['ITINERARY_ID' => $itinerary->getId()]);

        return $results_raw->fetchAllAssociative();
    }

    public function getUsersWithCompletedItinerary($itinerary, $totalCourses)
    {
        $conn = $this->_em->getConnection();
        $sql = 'SELECT data.itinerary_id, data.user_id, data.course_id, data.ic_id, data.announcement_id, data.started_at, data.finished_at, data.valued_at,
            sum(case when data.finished_at IS NOT NULL AND data.valued_at IS NOT NULL then 1 else 0 end) as completed
            FROM
            (   
            SELECT ic.itinerary_id, uc.user_id, uc.course_id, ic.id AS ic_id, uc.announcement_id, uc.started_at, uc.finished_at, uc.valued_at, ucc.time_spent
                FROM itinerary_course ic
                LEFT JOIN course c ON ic.course_id = c.translation_id
                INNER JOIN user_course uc ON (uc.course_id = ic.course_id OR (uc.course_id = c.id AND ic.course_id = c.translation_id ))
                        AND uc.announcement_id IS NULL
                INNER JOIN user_course_chapter ucc ON uc.id = ucc.user_course_id
                WHERE ic.itinerary_id = :ITINERARY_ID 
                GROUP BY ic.itinerary_id, uc.user_id
            ) AS data
            GROUP BY data.user_id';

        $query = $conn->prepare($sql);

        $results_raw = $query->executeQuery(['ITINERARY_ID' => $itinerary->getId()]);
        $results = $results_raw->fetchAllAssociative();

        $completed = [];

        foreach ($results as $user) {
            if ($user['completed'] == $totalCourses) {
                array_push($user);
            }
        }

        return $completed;
    }

    public function getUsersTotalsPaginated($itinerary, $currentPage, $limit)
    {
        $conn = $this->_em->getConnection();
        /*  QUERY CONTANDO TRADUCCIONES MEDIANTE CONNECTION QUE FUNCIONA */
        $sql = 'SELECT iu.itinerary_id, iu.user_id, u.email, uc.course_id, ic.id AS ic_id, uc.announcement_id, uc.started_at, uc.finished_at, uc.valued_at, ucc.time_spent
                    FROM itinerary_user iu
                    INNER JOIN itinerary_course ic ON ic.itinerary_id = iu.itinerary_id
                    LEFT JOIN course c ON ic.course_id = c.translation_id
                    INNER JOIN user_course uc ON (uc.course_id = ic.course_id OR (uc.course_id = c.id AND ic.course_id = c.translation_id ))
                            AND uc.user_id = iu.user_id AND uc.announcement_id IS NULL
                    INNER JOIN user_course_chapter ucc ON uc.id = ucc.user_course_id
                    INNER JOIN user u ON iu.user_id = u.id
                    WHERE iu.itinerary_id = :ITINERARY_ID
                    GROUP BY iu.user_id, ic_id';

        $stmt = $conn->prepare($sql);

        $query = $conn->createQueryBuilder();
        $types = [];

        $cacheStmt = $conn->executeCacheQuery($sql, ['ITINERARY_ID' => $itinerary->getId()], $types, new QueryCacheProfile(0, 'some key'));

        $paginator = $this->paginate($query, $currentPage, $limit);
        $maxPages = 10; // ceil($paginator->count() / $limit);

        $allResults = [];

        // TESTED PAGINATION

        // ->leftJoin(UserExtra::class,'ue',\Doctrine\ORM\Query\Expr\Join::WITH,'ue.user = u.id')
        // ->innerJoin('u', 'phonenumbers', 'p', 'u.id = p.user_id')

        /* QUERY MEDIANTE DOCTRINE BUILDER
        $query = $this->_em->createQueryBuilder()
        ->select(['iu.id AS iu_id','ic.id AS ic_id','uc.startedAt','uc.finishedAt','uc.valuedAt']) //,'ucc.timeSpent' ])
        ->from(ItineraryUser::class, 'iu')
        ->innerJoin("App\Entity\ItineraryCourse",'ic','ic.itinerary_id = iu.itinerary_id')
        ->leftJoin("App\Entity\Course","c","c.id = ic.course_id")
        ->innerJoin("App\Entity\UserCourse",'uc', "uc.user_id = iu.user_id ") // AND uc.course_id = ic.course_id")
        //->innerJoin("App\Entity\UserCourseChapter",'ucc','uc.id = ucc.user_course_id')
        ->innerJoin("iu.user","u")
        ->andWhere('iu.itinerary = :itinerary_id')
        ->andWhere('uc.announcement IS NULL')
        ->setParameter('itinerary_id', $itinerary->getId() )
        ->groupBy('iu_id','ic_id')
        ->setFirstResult(0)
        ->setMaxResults(100);

        //->innerJoin("App\Entity\ItineraryCourse","ic")
        //->leftJoin("App\Entity\Course","c",Expr\Join::ON, "ic.course = c.translation_id")


        $paginator   = [];//$this->paginate($query->getQuery(), $currentPage, $limit);
        $maxPages = 10;//ceil($paginator->count() / $limit);

        $allResults = $query->getQuery()->getResult();
         */

        /* USANDO DQL A MANO
        $dql = "SELECT iu, u, c
                FROM App:ItineraryUser iu
                INNER JOIN App:User u
                LEFT JOIN App:Course c
                WHERE iu.itinerary = 1
                GROUP BY iu.user";
        $query = $this->_em->createQuery($dql)
        ->setFirstResult(0)
        ->setMaxResults(100);

        $paginator = $this->paginate($query, $currentPage, $limit);
        $maxPages = ceil($paginator->count() / $limit);

        $allResults = $query->getResult();
        */

        // return array('paginator' => $paginator, 'allResults' => $allResults, 'maxPages' => $maxPages, 'query' => $query->getQuery()->getSQL() );
        return ['paginator' => $paginator, 'allResults' => $allResults, 'maxPages' => $maxPages, 'query' => $query];
    }

    public function getUsersTotalsFromItineraryByCompletionStatus(Itinerary $itinerary, bool $statsItinerary = false)
    {
        $conn = $this->_em->getConnection();
        if ($statsItinerary) {
            $query = $conn->prepare(
                'SELECT data.itinerary_id, data.user_id AS id, data.email, data.course_id, data.ic_id, data.announcement_id, data.started_at, data.finished_at, data.valued_at,
                sum(case when data.finished_at IS NOT NULL AND data.valued_at IS NOT NULL then 1 else 0 end) as completed,
                sum(case when data.valued_at IS NULL AND data.started_at IS NOT NULL then 1 else 0 end) as started,
                sum(data.time_spent) as total_time
                FROM
                ( 
                    SELECT iu.itinerary_id, iu.user_id, u.email, uc.course_id, ic.id AS ic_id, uc.announcement_id, uc.started_at, uc.finished_at, uc.valued_at, ucc.time_spent
                    FROM itinerary_user iu
                    INNER JOIN itinerary_course ic ON ic.itinerary_id = iu.itinerary_id
                    LEFT JOIN course c ON ic.course_id = c.translation_id
                    INNER JOIN user_course uc ON (uc.course_id = ic.course_id OR (uc.course_id = c.id AND ic.course_id = c.translation_id ))
                            AND uc.user_id = iu.user_id AND uc.announcement_id IS NULL
                    INNER JOIN user_course_chapter ucc ON uc.id = ucc.user_course_id
                    INNER JOIN user u ON iu.user_id = u.id
                    WHERE iu.itinerary_id = :ITINERARY_ID
                    GROUP BY iu.user_id, ic_id
                ) AS data
                GROUP BY data.user_id'
            );
        } else {
            $query = $conn->prepare(
                'SELECT data.itinerary_id, data.user_id, data.email, data.course_id, data.ic_id, data.announcement_id, data.started_at, data.finished_at, data.valued_at,
                sum(case when data.finished_at IS NOT NULL AND data.valued_at IS NOT NULL then 1 else 0 end) as completed,
                sum(case when data.valued_at IS NULL AND data.started_at IS NOT NULL then 1 else 0 end) as started,
                sum(data.time_spent) as total_time
                FROM
                ( 
                    SELECT iu.itinerary_id, iu.user_id, u.email, uc.course_id, ic.id AS ic_id, uc.announcement_id, uc.started_at, uc.finished_at, uc.valued_at, ucc.time_spent
                    FROM itinerary_user iu
                    INNER JOIN itinerary_course ic ON ic.itinerary_id = iu.itinerary_id
                    LEFT JOIN course c ON ic.course_id = c.translation_id
                    INNER JOIN user_course uc ON (uc.course_id = ic.course_id OR (uc.course_id = c.id AND ic.course_id = c.translation_id ))
                            AND uc.user_id = iu.user_id AND uc.announcement_id IS NULL
                    INNER JOIN user_course_chapter ucc ON uc.id = ucc.user_course_id
                    INNER JOIN user u ON iu.user_id = u.id
                    WHERE iu.itinerary_id = :ITINERARY_ID
                    GROUP BY iu.user_id, ic_id
                ) AS data
                GROUP BY data.user_id LIMIT 1'
            );
        }

        return $query->executeQuery(['ITINERARY_ID' => $itinerary->getId()])->fetchAllAssociative();
    }

    public function getUsersByItineraryFilters(Itinerary $itinerary, ?User $manager = null)
    {
        $courses = $itinerary->getItineraryCourses();
        $coursesId = [];
        foreach ($courses as $course) {
            $coursesId[] = $course->getCourse()->getId();
        }

        $users = $this->getUsersIdsFromItineraryFilters($itinerary, $manager);

        $usersIds = [];

        if (null != $users) {
            foreach ($users as $u) {
                $usersIds[] = (int) $u['id'];
            }

            return $this->calculateStartedAndCompletedCourses($usersIds, $coursesId);
        }

        return null;
    }

    public function getUsersExcelFile(Itinerary $itinerary, ?User $manager = null)
    {
        $courses = $itinerary->getItineraryCourses();
        $coursesId = [];
        foreach ($courses as $course) {
            $coursesId[] = $course->getCourse()->getId();
        }

        $users = $this->getUsersIdsFromItineraryFilters($itinerary, $manager);

        $users = (empty($users))
            ? $this->getUsersFromItineraryUser(itinerary: $itinerary, manager: $manager)
            : array_merge($users, $this->getUsersFromItineraryUser(itinerary: $itinerary, manager: $manager));

        $usersIds = [];
        foreach ($users as $u) {
            $usersIds[] = (int) $u['id'];
        }
        $usersIds = array_unique($usersIds, SORT_NUMERIC);

        return $this->calculateStartedAndCompletedCourses($usersIds, $coursesId);
    }

    public function getUsersPaginated(Itinerary $itinerary, $filters, ?User $manager = null)
    {
        $courses = $itinerary->getItineraryCourses();
        $coursesId = [];
        foreach ($courses as $course) {
            $coursesId[] = $course->getCourse()->getId();
        }

        $users = $this->getUsersFromItineraryUser(itinerary: $itinerary, filters: $filters, manager: $manager);
        $usersIds = [];
        foreach ($users as $u) {
            $usersIds[] = (int) $u['id'];
        }

        return $this->calculateStartedAndCompletedCourses($usersIds, $coursesId);
    }

    /**
     * Creates an Excel file with different sheets related to the itinerary:
     *  1) General itinerary information sheet (Itinerary info).
     *  2) Courses information sheet (Courses info).
     *  3) Users and their statistics sheet (Itinerary users).
     *  4) Users vs. Courses relationship sheet (User - Courses).
     */
    public function generateExcelFile(Itinerary $itinerary, ?\DateTimeInterface $createdAt = null, ?User $manager = null): Spreadsheet
    {
        $spreadsheet = new Spreadsheet();

        $this->buildItineraryInfoSheet($spreadsheet, $itinerary, $createdAt);
        $this->buildCoursesInfoSheet($spreadsheet, $itinerary);
        $this->buildItineraryUsersSheet(spreadsheet: $spreadsheet, itinerary: $itinerary, manager: $manager);
        $this->buildUserCoursesSheet($spreadsheet, $itinerary);

        return $spreadsheet;
    }

    /**
     * Builds the "Itinerary info" sheet.
     */
    private function buildItineraryInfoSheet(Spreadsheet $spreadsheet, Itinerary $itinerary, ?\DateTimeInterface $createdAt): void
    {
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setTitle('Itinerary info');

        if ($createdAt) {
            $sheet->setCellValue('A1', 'Requested Date')
                ->setCellValue('B1', $createdAt->format('Y-m-d'));
        }

        $totalTime = $this->getTimeSpentInCourses($itinerary);
        $sheet->setCellValue('A2', 'Total Time')
            ->setCellValue('B2', TimeUtils::formatTime($totalTime['total_time']));
        $sheet->setCellValue('A3', 'Total Courses')
            ->setCellValue('B3', $totalTime['count_courses']);

        foreach (range('A', 'C') as $columnID) {
            $sheet->getColumnDimension($columnID)->setAutoSize(true);
        }
    }

    /**
     * Builds the "Courses info" sheet.
     */
    private function buildCoursesInfoSheet(Spreadsheet $spreadsheet, Itinerary $itinerary): void
    {
        $sheet = $spreadsheet->createSheet();
        $sheet->setTitle('Courses info');

        $headers = ['Código', 'Nombre', 'Categoría'];
        $sheet->fromArray($headers, null, 'A1');
        $sheet->getStyle('A1:C1')->getFont()->setBold(true);

        foreach (range('A', 'C') as $columnID) {
            $sheet->getColumnDimension($columnID)->setAutoSize(true);
        }

        $row = 2;
        foreach ($itinerary->getItineraryCourses() as $itineraryCourse) {
            $course = $itineraryCourse->getCourse();
            $sheet->fromArray(
                [
                    'code' => $course->getCode(),
                    'name' => $course->getName(),
                    'category' => $course->getCategory() ? $course->getCategory()->getName() : '',
                ],
                null,
                "A$row"
            );
            ++$row;
        }
    }

    /**
     * Builds the "Itinerary users" sheet.
     */
    private function buildItineraryUsersSheet(Spreadsheet $spreadsheet, Itinerary $itinerary, ?User $manager = null): void
    {
        $sheet = $spreadsheet->createSheet();
        $sheet->setTitle('Itinerary users');

        $headers = ['User Id', 'Nombre', 'Apellidos', 'Email', 'Género'];

        $filterCategory = $this->_em->getRepository(FilterCategory::class)->findAll();
        $categoryIds = [];
        $categories = [];

        foreach ($filterCategory as $category) {
            $categories[] = $category->getName();
            $categoryIds[] = $category->getId();
        }

        $headers = array_merge($headers, $categories, [
            'Cursos Completados',
            'Cursos Iniciados',
            'Tiempo acumulado',
        ]);

        $sheet->fromArray($headers, null, 'A1');
        $lastColumn = $sheet->getHighestColumn(1);
        $sheet->getStyle("A1:{$lastColumn}1")->getFont()->setBold(true);

        foreach (range('A', $lastColumn) as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }

        $itineraryUsers = $this->getUsersExcelFile($itinerary, $manager);
        if (empty($itineraryUsers)) {
            return;
        }

        $row = 2;
        $userIds = array_column($itineraryUsers, 'id');

        $filterRepo = $this->_em->getRepository(Filter::class);
        $allFilters = $filterRepo->fetchAllFiltersForUsers($userIds, $categoryIds);

        $groupedFilters = [];
        foreach ($allFilters as $rowFilter) {
            $uId = $rowFilter['userId'];
            $cId = $rowFilter['categoryId'];
            $groupedFilters[$uId][$cId][] = $rowFilter['filterName'];
        }

        foreach ($itineraryUsers as $userData) {
            $userRow = [
                'id' => $userData['id'],
                'first_name' => $userData['first_name'],
                'last_name' => $userData['last_name'],
                'email' => $userData['email'],
                'gender' => $userData['gender'],
            ];

            foreach ($categoryIds as $catId) {
                $filterNames = $groupedFilters[$userData['id']][$catId] ?? [];
                $userRow['category_' . $catId] = implode(',', $filterNames);
            }

            $userRow['completed'] = $userData['completed'];
            $userRow['started'] = $userData['started'];
            $userRow['total_time'] = TimeUtils::formatTime($userData['total_time']);

            $sheet->fromArray([$userRow], null, "A$row");
            ++$row;
        }
    }

    /**
     * Builds the "User - Courses" sheet.
     */
    private function buildUserCoursesSheet(Spreadsheet $spreadsheet, Itinerary $itinerary): void
    {
        $sheet = $spreadsheet->createSheet();
        $sheet->setTitle('User - Courses');

        $headers = ['User ID', 'Nombre', 'Apellidos', 'Email'];
        $subHeaders = ['', '', '', ''];

        $courses = [];
        foreach ($itinerary->getItineraryCourses() as $itineraryCourse) {
            $courses[] = $itineraryCourse->getCourse();
        }

        foreach ($courses as $course) {
            $headers[] = $course->getName();
            $headers[] = '';
            $subHeaders[] = 'Tiempo Acumulado';
            $subHeaders[] = 'Fecha Fin';
        }

        $sheet->fromArray($headers, null, 'A1');
        $sheet->fromArray($subHeaders, null, 'A2');

        $lastColumn = $sheet->getHighestColumn(1);
        $sheet->getStyle("A1:{$lastColumn}1")->getFont()->setBold(true);

        // Convert column letter to numeric index
        $lastColumnIndex = Coordinate::columnIndexFromString($lastColumn);

        // Iterate through numeric index and convert back to column letter
        for ($colIdx = 1; $colIdx <= $lastColumnIndex; ++$colIdx) {
            $col = Coordinate::stringFromColumnIndex($colIdx);
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }

        $columnNames = [];
        for ($col = 'E'; $col !== $lastColumn; ++$col) {
            $columnNames[] = $col;
        }

        $total = \count($columnNames);
        $colIndex = 5;
        for ($index = 0; $index < ($total - 1); $index += 2) {
            $colLeft = $columnNames[$index];
            $colRight = $columnNames[$index + 1];
            $courseName = $sheet->getCell([$colIndex, 1])->getValue();

            $sheet->mergeCells("$colLeft" . '1:' . "$colRight" . '1');
            $sheet->getCell([$colIndex, 1])->setValue($courseName);

            $colIndex += 2;
        }

        $itineraryUsers = $this->getUsersExcelFile($itinerary);
        if (empty($itineraryUsers)) {
            return;
        }

        $userIds = array_column($itineraryUsers, 'id');
        $courseIds = array_map(fn ($c) => $c->getId(), $courses);

        $data = $this->fetchUserCoursesData($userIds, $courseIds);

        $userCourseMap = [];
        foreach ($data as $rowItem) {
            $uId = (int) $rowItem['userId'];
            $cId = (int) $rowItem['courseId'];
            $userCourseMap[$uId][$cId] = [
                'finishedAt' => $rowItem['latestFinishedAt'] ?? null,
                'totalTime' => $rowItem['totalTime'] ? (int) $rowItem['totalTime'] : null,
            ];
        }

        $row = 3;
        foreach ($itineraryUsers as $userData) {
            $rowValues = [
                'id' => $userData['id'],
                'first_name' => $userData['first_name'],
                'last_name' => $userData['last_name'],
                'email' => $userData['email'],
            ];

            foreach ($courses as $course) {
                $cId = $course->getId();
                if (isset($userCourseMap[$userData['id']][$cId])) {
                    $info = $userCourseMap[$userData['id']][$cId];
                    $finishedAt = \is_scalar($info['finishedAt']) ? (string) $info['finishedAt'] : null;
                    $totalTime = \is_scalar($info['totalTime']) ? TimeUtils::formatTime($info['totalTime']) : null;
                } else {
                    $finishedAt = null;
                    $totalTime = null;
                }

                $rowValues[$cId . '_totalTime'] = $totalTime;
                $rowValues[$cId . '_finishedAt'] = $finishedAt;
            }

            try {
                $sheet->fromArray($rowValues, null, 'A' . $row++);
            } catch (\Throwable $e) {
                $this->logger->error('[ItineraryRepository] EXCEPTION in fromArray: ' . $e->getMessage());
            }
        }

        $lastColumnIndex = Coordinate::columnIndexFromString($sheet->getHighestColumn(1));
        foreach (range(1, $lastColumnIndex) as $columnID) {
            $sheet->getColumnDimensionByColumn($columnID)->setAutoSize(true);
        }
    }

    /**
     * Auxiliary method to collect data on courses completed by users.
     */
    public function fetchUserCoursesData(array $userIds, array $courseIds): array
    {
        if (empty($userIds) || empty($courseIds)) {
            return [];
        }

        $qb = $this->_em->createQueryBuilder();

        $qb->select([
            'IDENTITY(uc.user) AS userId',
            'COALESCE(IDENTITY(c.translation), c.id) AS courseId',
            'MAX(uc.finishedAt) AS latestFinishedAt',
            'SUM(ucc.timeSpent) AS totalTime',
        ])
            ->from(UserCourse::class, 'uc')
            ->innerJoin('uc.chapters', 'ucc')
            ->innerJoin('uc.course', 'c')
            ->where($qb->expr()->in('IDENTITY(uc.user)', ':userIds'))
            ->andWhere('uc.announcement IS NULL')
            ->andWhere('uc.finishedAt IS NOT NULL')
            ->andWhere($qb->expr()->orX(
                $qb->expr()->in('c.id', ':courseIds'),
                $qb->expr()->in('IDENTITY(c.translation)', ':courseIds')
            ))
            ->setParameter('userIds', $userIds)
            ->setParameter('courseIds', $courseIds)
            ->groupBy('userId, courseId');

        return $qb->getQuery()->getResult(Query::HYDRATE_ARRAY);
    }

    public function findByUserAndCourse(User $user, Course $course)
    {
        $qb = $this->createQueryBuilder('i')
            ->innerJoin('i.itineraryCourses', 'ic')
            ->innerJoin('i.itineraryUsers', 'iu')
            ->andWhere('ic.course = :course')
            ->andWhere('iu.user = :user')
            ->setParameter('course', $course)
            ->setParameter('user', $user);

        return $qb->getQuery()->getResult();
    }

    /**
     * Returns the total time and the number of courses in the itinerary.
     */
    public function getTimeSpentInCourses(Itinerary $itinerary, ?User $manager = null)
    {
        $courses = $itinerary->getItineraryCourses();
        $coursesId = [];

        foreach ($courses as $course) {
            $c = $course->getCourse();
            $coursesId[] = $c->getId();
            foreach ($c->getTranslations() as $translation) {
                $coursesId[] = $translation->getId();
            }
        }

        $users = $this->getUsersIdsFromItineraryFilters(itinerary: $itinerary, manager: $manager);
        $users = (empty($users))
            ? $this->getUsersFromItineraryUser(
                itinerary: $itinerary,
                manager: $manager
            )
            : array_merge($users, $this->getUsersFromItineraryUser(itinerary: $itinerary, manager: $manager));

        $usersIds = [];
        foreach ($users as $u) {
            $usersIds[] = (int) $u['id'];
        }
        $usersIds = array_unique($usersIds, SORT_NUMERIC);

        if (empty($usersIds)) {
            return [
                'total_time' => 0,
                'count_courses' => $itinerary->getTotalCourses(),
            ];
        }

        $resultSetMapping = new ResultSetMapping();
        $resultSetMapping->addScalarResult('id', 'id', 'integer');

        $query = $this->_em->createNativeQuery('
            SELECT 
                uc.id as id
            FROM user u
            JOIN (SELECT * FROM user_course uc1 WHERE uc1.course_id IN (:COURSES_ID)) uc ON uc.user_id = u.id
            LEFT JOIN course c ON c.id = uc.course_id
            WHERE u.id IN (:USER_IDS)
        ', $resultSetMapping)->setParameters([
            'USER_IDS' => $usersIds,
            'COURSES_ID' => $coursesId,
        ]);

        $userCourseIds = $query->getSingleColumnResult();
        if (empty($userCourseIds)) {
            return [
                'total_time' => 0,
                'count_courses' => $itinerary->getTotalCourses(),
            ];
        }

        $totalTime = $this->_em->getRepository(UserCourseChapter::class)
            ->createQueryBuilder('ucc')
            ->select('SUM(ucc.timeSpent) as timeSpent')
            ->where('ucc.userCourse IN (:IDS)')
            ->setParameter('IDS', $userCourseIds)
            ->getQuery()
            ->getSingleScalarResult();

        return [
            'total_time' => $totalTime,
            'count_courses' => $itinerary->getTotalCourses(),
        ];
    }

    public function findUsersItineraryCreators(): array
    {
        $itinerary = $this->createQueryBuilder('i')
            ->select('i')
            ->join('i.createdBy', 'u')
            ->distinct('u.id')
            ->getQuery()
            ->getResult();

        $list = [];

        /** @var Itinerary $i */
        foreach ($itinerary as $i) {
            $u = $i->getCreatedBy();
            $list[$u->getId()] = $u->getEmail();
        }

        asort($list);

        return $list;
    }

    public function findItineraryCloseByCountry(array $conditions = [])
    {
        $query = $this->createQueryBuilder('i')
            ->select('count(i.id) as count, uex.country, 0 as countryName')
            ->leftJoin(
                ItineraryCourse::class,
                'ic',
                Expr\Join::WITH,
                'i.id = ic.itinerary'
            )
            ->leftJoin(
                UserCourse::class,
                'uc',
                Expr\Join::WITH,
                'ic.course = uc.course'
            )
            ->leftJoin(
                UserExtra::class,
                'uex',
                Expr\Join::WITH,
                'uc.user = uex.user'
            )
            ->andWhere('uc.finishedAt is not null')
            ->andWhere('uex.deletedAt is null')
            ->andWhere('i.deletedAt is null')
            ->andWhere('uex.country is not null')
            ->andWhere("uex.country <> ''")
            ->groupBy('uex.country');

        if (isset($conditions['active']) || isset($conditions['country']) || isset($conditions['center']) || !empty($conditions['category']) || !empty($conditions['departament']) || !empty($conditions['gender']) || !empty($conditions['division']) || !empty($conditions['filters'])) {
            $query->leftJoin('uc.user', 'u')
                ->leftJoin('u.extra', 'ue');

            $this->setQueryFilters($query, $conditions);
        }

        if (!empty($conditions['dateFrom'])) {
            $query->andWhere('i.createdAt >= :dateFrom')->setParameter('dateFrom', $conditions['dateFrom']);
        }
        if (!empty($conditions['dateTo'])) {
            $query->andWhere('i.createdAt <= :dateTo')->setParameter('dateTo', $conditions['dateTo']);
        }

        return $query
            ->getQuery()
            ->getArrayResult();
    }

    public function itineraryClosedAndProgress($conditions, $closed)
    {
        $query = $this->createQueryBuilder('i')
            ->select('count(ic.id) as count, i.name')
            ->leftJoin(
                ItineraryCourse::class,
                'ic',
                Expr\Join::WITH,
                'i.id = ic.itinerary'
            )
            ->leftJoin(
                UserCourse::class,
                'uc',
                Expr\Join::WITH,
                'ic.course = uc.course'
            )
            ->andWhere('i.deletedAt is null')
            ->groupBy('i.name');

        if (isset($conditions['active']) || isset($conditions['country']) || isset($conditions['center']) || !empty($conditions['category']) || !empty($conditions['departament']) || !empty($conditions['gender']) || !empty($conditions['division']) || !empty($conditions['filters'])) {
            $query->leftJoin('uc.user', 'u')
                ->leftJoin('u.extra', 'ue');

            $this->setQueryFilters($query, $conditions);
        }

        if (!empty($conditions['dateFrom'])) {
            $query->andWhere('i.createdAt >= :dateFrom')->setParameter('dateFrom', $conditions['dateFrom']);
        }
        if (!empty($conditions['dateTo'])) {
            $query->andWhere('i.createdAt <= :dateTo')->setParameter('dateTo', $conditions['dateTo']);
        }

        if ($closed) {
            $query = $query->andWhere('uc.finishedAt is not null');
        }

        return $query
            ->getQuery()
            // ->getSQL();
            ->getArrayResult();
    }

    /**
     * @throws Exception
     * @throws \Doctrine\DBAL\Driver\Exception
     */
    public function getCoursesCompletionStats(Itinerary $itinerary): array
    {
        $conn = $this->_em->getConnection();

        $totalFilters = '
			SELECT COUNT(DISTINCT fc.id) as cantidad
			FROM itinerary_filter itf
			LEFT JOIN filter f ON f.id = itf.filter_id
			LEFT JOIN filter_category fc ON fc.id = f.filter_category_id 
			WHERE itinerary_id = :ITINERARY_ID';

        $usersByFilters = "
			SELECT uf.user_id, COUNT(DISTINCT groups.category_id) as totalFilters
			FROM (
				SELECT CONCAT('-', GROUP_CONCAT(itf.filter_id SEPARATOR '-'), '-') as ids, fc.id as category_id
				FROM itinerary_filter itf
				LEFT JOIN filter f ON f.id = itf.filter_id
				LEFT JOIN filter_category fc ON fc.id = f.filter_category_id 
				WHERE itinerary_id = :ITINERARY_ID
				GROUP BY fc.id
			) `groups`
			LEFT JOIN user_filter uf ON ids LIKE CONCAT('%-', uf.filter_id, '-%')
			JOIN `user` u0 ON u0.id = uf.user_id AND u0.is_active = 1 AND u0.deleted_at IS NULL
			GROUP BY uf.user_id
			HAVING totalFilters = ($totalFilters)";

        $usersByItinerary = '
			SELECT iu1.user_id, 0 as totalFilters 
			FROM itinerary_user iu1
			JOIN user u1 ON u1.id = iu1.user_id AND u1.deleted_at IS NULL
			WHERE iu1.itinerary_id = :ITINERARY_ID';

        $courseData = '
			SELECT user_id, course_id, finished_at, started_at 
			FROM user_course 
			WHERE announcement_id IS NULL 
			GROUP BY user_id, course_id, announcement_id';

        $query = $conn->prepare("
	  	SELECT 
			c.`name` as course, 
			COUNT(DISTINCT iu.user_id) as total, 
			SUM(IF(uc.finished_at IS NOT NULL, 1, 0)) as completed, 
			SUM(IF(uc.started_at IS NOT NULL AND uc.finished_at IS NULL, 1, 0)) as started,
			SUM(IF(uc.started_at IS NULL, 1, 0)) as unstarted
		FROM (SELECT id, course_id FROM itinerary_course WHERE itinerary_id = :ITINERARY_ID) ic
		JOIN (
			SELECT usersUnion.user_id AS user_id, :ITINERARY_ID AS itinerary_id
			FROM (($usersByFilters) UNION ($usersByItinerary)) usersUnion 
			GROUP BY usersUnion.user_id
		) iu on iu.itinerary_id = :ITINERARY_ID
		LEFT JOIN course c ON c.id = ic.course_id
		LEFT JOIN ($courseData) uc on uc.user_id = iu.user_id AND uc.course_id = ic.course_id
		GROUP BY ic.id;");

        return $query->executeQuery(['ITINERARY_ID' => $itinerary->getId()])->fetchAllAssociative();
    }

    public function groupItineraryInfoToExcel(array $conditions = [])
    {
        $query = $this->createQueryBuilder('i')
            ->select('count(i.id) as count, uex.country, 0 as countryName')
            ->leftJoin(
                ItineraryCourse::class,
                'ic',
                Expr\Join::WITH,
                'i.id = ic.itinerary'
            )
            ->leftJoin(
                UserCourse::class,
                'uc',
                Expr\Join::WITH,
                'ic.course = uc.course'
            )
            ->leftJoin(
                UserExtra::class,
                'uex',
                Expr\Join::WITH,
                'uc.user = uex.user'
            )
            ->andWhere('uc.finishedAt is not null')
            ->andWhere('uex.deletedAt is null')
            ->andWhere('i.deletedAt is null')
            ->andWhere('uex.country is not null')
            ->andWhere("uex.country <> ''")
            ->groupBy('uex.country');

        return $query
            ->getQuery()
            ->getArrayResult();
    }

    public function totalItineraryForExcel($content)
    {
        $query = $this->createQueryBuilder('i')
            ->select('i.id')
            ->leftJoin(
                ItineraryCourse::class,
                'ic',
                Expr\Join::WITH,
                'i.id = ic.itinerary'
            )
            ->leftJoin(
                Course::class,
                'c',
                Expr\Join::WITH,
                'c.id = ic.course'
            )
            ->groupBy('i.id');

        if (!empty($content['tags'])) {
            $tags = json_decode($content['tags']);
            if (isset($tags) && \is_array($tags)) {
                foreach ($tags as $key => $item) {
                    $param = ':filterTagID' . $key;
                    $query
                        ->andWhere('i.tags like ' . $param)
                        ->setParameter($param, '%[' . $item . ']%');
                }
            }
        }
        if (!empty($content['courseId'])) {
            $query
                ->andWhere('c.id = :course')
                ->setParameter('course', $content['courseId']);
        }

        return $query
            ->getQuery()
            ->getArrayResult();
    }

    public function getCoursesCountFromItineraries()
    {
        /*
        $query =  $this->_em->createQueryBuilder()
            ->select('COUNT(ic.course_id) as totales, COUNT(DISTINCT (ic.course_id)) as unicos')
            ->from('App:ItineraryCourse', 'ic')
            ->leftJoin(
                Itinerary::class,
                'i',
                \Doctrine\ORM\Query\Expr\Join::WITH,
                'i.id = ic.itinerary'
            )
            ->leftJoin(
                Course::class,
                'c',
                \Doctrine\ORM\Query\Expr\Join::WITH,
                'c.id = ic.course'
            )
            ->where("i.deleted_at IS NULL AND c.deleted_at IS NULL");

              $result = $query->getQuery()->getArrayResult();

                return $result;
        */

        /*    $query->add('where',
                 $query->expr()->isNull('i.deleted_at'),
                $query->expr()->isNull('i.deleted_at')

        );*/
        $conn = $this->_em->getConnection();
        $query = $conn->prepare('SELECT COUNT(ic.course_id) as totales, COUNT(DISTINCT (ic.course_id)) as unicos 
		                FROM itinerary_course ic
		                LEFT JOIN course c ON c.id = ic.course_id
		                LEFT JOIN itinerary i ON i.id = ic.itinerary_id
		                WHERE i.deleted_at IS NULL AND c.deleted_at IS NULL ');

        $result = $query->executeQuery()->fetchAllAssociative();

        return $result[0];
    }

    public function detailItineraryForExcelAnnouncement($content)
    {
        $where = 'i.deleted_at is null and c.deleted_at is null ';
        $params = [];

        if (!empty($content['tags'])) {
            $tags = json_decode($content['tags']);
            if (isset($tags) && \is_array($tags)) {
                foreach (json_decode($content['tags']) as $item) {
                    $where = $where . " and i.tags like '%[" . $item . "]%'";
                }
            }
        }

        if (!empty($content['courseId'])) {
            $where .= ' and ic.course_id = :courseId';
            $params['courseId'] = $content['courseId'];
        }

        $conn = $this->_em->getConnection();
        $query = $conn->prepare('
            select distinct i.id, i.name
            , (select distinct a.name
                from filter a
                inner join itinerary_filter c on c.filter_id = a.id
                where a.filter_category_id=1 and c.itinerary_id = i.id Limit 1
            ) as division
            , (select distinct a.name
                from filter a
                inner join itinerary_filter c on c.filter_id = a.id
                where a.filter_category_id=4 and c.itinerary_id = i.id Limit 1
            ) as departament
            , (select count(distinct(a.course_id)) from itinerary_course a where a.itinerary_id = i.id) as cursos_asignados
            , 0 as personas_asignadas
            , 0 as personas_completo_itineraro
            , 0 as personas_en_proceso
            , 0 as personas_sin_empezar
            , 0 as total_tiempo
            , 0 as tiempo_medio_persona
            from itinerary i
            left join itinerary_course ic on ic.itinerary_id = i.id
            left join course c on c.id = ic.course_id
            where ' . $where . '
        ');

        if (!empty($params)) {
            foreach ($params as $key => $value) {
                $query->bindParam($key, $value);
            }
        }

        return $query->executeQuery()->fetchAllAssociative();
    }

    public function detailCoursesForExcelAnnouncement($content)
    {
        $where = 'i.deleted_at is null ';
        $params = [];

        if (!empty($content['tags'])) {
            $tags = json_decode($content['tags']);
            if (isset($tags) && \is_array($tags)) {
                foreach (json_decode($content['tags']) as $item) {
                    $where = $where . " and i.tags like '%[" . $item . "]%'";
                }
            }
        }

        if (!empty($content['courseId'])) {
            $where .= ' and ic.course_id = :courseId';
            $params['courseId'] = $content['courseId'];
        }

        $conn = $this->_em->getConnection();
        $query = $conn->prepare('
            select distinct i.id, i.name, c.name as curso
            , 0 as personas_completo_itineraro
            , 0 as personas_en_proceso
            , 0 as personas_sin_empezar
            , c.id as courseId
            from itinerary i
            inner join itinerary_course ic on ic.itinerary_id = i.id
            inner join course c on c.id = ic.course_id
            where ' . $where . ' order By i.id asc
        ');

        if (!empty($params)) {
            foreach ($params as $key => $value) {
                $query->bindParam($key, $value);
            }
        }

        return $query->executeQuery()->fetchAllAssociative();
    }

    public function getUsersPaginatedByCourse(Itinerary $itinerary, $courseId, ?User $manager = null)
    {
        $coursesId = [$courseId];
        $users = $this->getUsersFromItineraryUser(itinerary: $itinerary, manager: $manager);
        $usersIds = [];

        foreach ($users as $u) {
            $usersIds[] = (int) $u['id'];
        }

        return $this->calculateStartedAndCompletedCourses($usersIds, $coursesId);
    }

    public function getUsersByItineraryFiltersByCourse(Itinerary $itinerary, $coursesId, $manager = null)
    {
        $coursesId = [$coursesId];
        $users = $this->getUsersIdsFromItineraryFilters($itinerary, $manager);
        $usersIds = [];

        if ($users) {
            foreach ($users as $u) {
                $usersIds[] = (int) $u['id'];
            }

            return $this->calculateStartedAndCompletedCourses($usersIds, $coursesId);
        }

        return null;
    }

    public function getUsersFilters(Itinerary $itinerary): array
    {
        $conn = $this->_em->getConnection();
        $query = $conn->prepare('
                SELECT u.id, email, first_name as firstName, last_name  as lastName,
                (SELECT GROUP_CONCAT(filter_id) FROM user_filter uf WHERE uf.user_id = u.id GROUP BY u.id) AS filters
                FROM itinerary_user iu 
                LEFT JOIN user u ON iu.user_id = u.id   
                WHERE iu.itinerary_id = :ITINERARY_ID
            ');

        return $query->executeQuery(['ITINERARY_ID' => $itinerary->getId()])->fetchAllAssociative();
    }

    public function getUsersIdsByFiltersAndManual(int $itineraryId): array
    {
        return [
            'manual' => $this->getManualUserIds($itineraryId),
            'filter' => $this->getFilterUserIds($itineraryId),
        ];
    }

    private function executeQuery(QueryBuilder $qb, ?string $errorContext = '', ?int $itineraryId = null): array
    {
        try {
            return $qb->getQuery()->getArrayResult();
        } catch (\Exception $e) {
            $this->logger->error("[ItineraryRepository] Error obtaining {$errorContext} user IDs for Itinerary ID {$itineraryId}: " . $e->getMessage());

            return [];
        }
    }

    private function processUserIds(array $result): array
    {
        $userIds = array_map(function ($item) {
            return $item['userId'];
        }, $result);

        return array_values(array_unique($userIds));
    }

    private function getManualUserIds(int $itineraryId): array
    {
        $qb = $this->createQueryBuilder('i')
            ->select('u.id AS userId')
            ->join('i.itineraryUsers', 'iu')
            ->join('iu.user', 'u')
            ->where('i.id = :id')
            ->groupBy('u.id')
            ->setParameter('id', $itineraryId);

        $result = $this->executeQuery($qb, 'manual', $itineraryId);

        return $this->processUserIds($result);
    }

    private function getFilterUserIds(int $itineraryId): array
    {
        $qb = $this->createQueryBuilder('i')
            ->select('u.id AS userId')
            ->join('i.filters', 'f')
            ->join('f.users', 'u')
            ->where('i.id = :id')
            ->groupBy('u.id')
            ->setParameter('id', $itineraryId);

        $result = $this->executeQuery($qb, 'filter', $itineraryId);

        return $this->processUserIds($result);
    }

    public function getItinerariesByCourse(Course $course): array
    {
        return $this->createQueryBuilder('it')
            ->select('it')
            ->join('it.itineraryCourses', 'ic')
            ->where('ic.course = :course')
            ->andWhere('it.active = 1')
            ->andWhere('it.deletedAt IS NULL')
            ->setParameter('course', $course)
            ->getQuery()
            ->getResult();
    }

    public function getMaxSort()
    {
        $maxSort = $this->createQueryBuilder('i')
            ->select('i.sort as maxSort')
            ->orderBy('maxSort', 'desc')
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();

        return null !== $maxSort ? $maxSort['maxSort'] + 1 : 1;
    }

    public function getCoursesItineraries(Itinerary $itinerary, int $page = 1, int $limit = 10): array
    {
        $totalCourses = $this->createQueryBuilder('i')
            ->select('COUNT(c.id)')
            ->join('i.itineraryCourses', 'ic')
            ->join('ic.course', 'c')
            ->where('i.id = :itinerary')
            ->setParameter('itinerary', $itinerary)
            ->getQuery()
            ->getSingleScalarResult();

        $query = $this->createQueryBuilder('i')
            ->select('c.id')
            ->join('i.itineraryCourses', 'ic')
            ->join('ic.course', 'c')
            ->where('i.id = :itinerary')
            ->setParameter('itinerary', $itinerary)
            ->setFirstResult(($page - 1) * $limit)
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();

        return [
            'courses' => $query,
            'totalPages' => (int) ceil($totalCourses / $limit),
            'totalItems' => (int) $totalCourses,
        ];
    }
}
