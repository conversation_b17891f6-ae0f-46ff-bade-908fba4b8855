<?php

declare(strict_types=1);

namespace App\Resources\DataFixtureBase\General;

class AppData
{
    public const CHAPTER_TYPES = [
        ['name' => 'Scorm', 'type' => 'content', 'active' => true],
        ['name' => 'Contents', 'type' => 'content', 'active' => true],
        ['name' => 'Roulette', 'type' => 'game', 'active' => true],
        ['name' => 'Double or Nothing', 'type' => 'game', 'active' => true],
        ['name' => 'Quiz', 'type' => 'game', 'active' => true],
        ['name' => 'Puzzle', 'type' => 'game', 'active' => true],
        ['name' => 'Hidden Words', 'type' => 'game', 'active' => true],
        ['name' => 'Pdf', 'type' => 'content', 'active' => true],
        ['name' => 'Video', 'type' => 'content', 'active' => true],
        ['name' => 'Slider', 'type' => 'content', 'active' => true],
        ['name' => 'ruleta', 'type' => 'game', 'active' => true],
        ['name' => 'True or false', 'type' => 'game', 'active' => true],
        ['name' => 'HiddenPic', 'type' => 'game', 'active' => true],
        ['name' => 'HigherLower', 'type' => 'game', 'active' => true],
        ['name' => 'MemoryMatch', 'type' => 'game', 'active' => true],
        ['name' => 'Categorized', 'type' => 'game', 'active' => true],
        ['name' => 'FillGaps', 'type' => 'game', 'active' => true],
        ['name' => 'GuessWord', 'type' => 'game', 'active' => true],
        ['name' => 'Wordle', 'type' => 'game', 'active' => true],
        ['name' => 'SearchWord', 'type' => 'game', 'active' => true],
        ['name' => 'VideoQuiz', 'type' => 'game', 'active' => true],
        ['name' => 'VCMS', 'type' => 'content', 'active' => true],
        ['name' => 'Roleplay', 'type' => 'content', 'active' => true],
    ];

    public const VIDEO_TYPE = [['name' => 'YouTube'], ['name' => 'Vimeo']];

    public const NPS_QUESTIONS = [
        [
            'type' => 'text',
            'position' => 1,
            'question' => 'Danos tu opinión',
            'main' => true,
            'source' => 1,
            'active' => true
        ],
        [
            'type' => 'nps',
            'position' => 2,
            'question' => 'Valoración del curso',
            'main' => true,
            'source' => 1,
            'active' => true
        ]
    ];

    public const CRON_JOBS = [
        [
            'name' => 'Run Task',
            'command' => 'task:execute',
            'schedule' => '* * * * *',
            'description' => 'Execute a programmed Task',
            'enabled' => true,
        ],
        [
            'name' => 'Add User Mainteneance Task',
            'command' => 'task:add user-maintenance',
            'schedule' => '0 4 * * *',
            'description' => 'Add a new User Mainteneance Task from las 24 hours',
            'enabled' => true,
        ],
    ];

    public const SETTING_SLIST = [
        [
            'group' => 'General',
            'settings' => [
                [
                    'code' => 'app.multilingual',
                    'value' => 'true',
                    'name' => 'Multi-idioma',
                    'sort' => 1,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => ' app.defaultLanguage',
                    'value' => 'es',
                    'name' => 'Idioma por defecto',
                    'sort' => 2,
                    'options' => [],
                    'type' => 'string',
                ],
                [
                    'code' => 'app.languages',
                    'value' => '["en", "pt", "es", "fr"]',
                    'name' => 'Languages',
                    'sort' => 3,
                    'options' => [],
                    'type' => 'array',
                ],
                [
                    'code' => 'app.zip.day_available_until',
                    'value' => '5',
                    'name' => 'Day available until',
                    'sort' => 13,
                    'options' => [],
                    'type' => 'string',
                ],
            ],
        ],
        [
            'group' => 'Courses',
            'settings' => [
                [
                    'code' => 'app.setCoursePoints',
                    'value' => 'false',
                    'name' => 'Set course points',
                    'sort' => 1,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.courseDefaultPoints',
                    'value' => '500',
                    'name' => 'Course default points',
                    'sort' => 2,
                    'options' => [],
                    'type' => 'string',
                ],
                [
                    'code' => 'app.courseInfoGeneral',
                    'value' => 'false',
                    'name' => 'Course documentation',
                    'sort' => 3,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.openCourse',
                    'value' => 'true',
                    'name' => 'Open course',
                    'sort' => 4,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.supportEmail',
                    'value' => '["<EMAIL>"]',
                    'name' => 'Support Email',
                    'sort' => 5,
                    'options' => [],
                    'type' => 'array',
                ],
            ],
        ],
        [
            'group' => 'Vimeo',
            'settings' => [
                [
                    'code' => 'app.clientIdVimeo',
                    'value' => '',
                    'name' => 'Client ID',
                    'sort' => 1,
                    'options' => [],
                    'type' => 'string',
                ],
                [
                    'code' => 'app.clientSecretVimeo',
                    'value' => '',
                    'name' => 'Client secret',
                    'sort' => 2,
                    'options' => [],
                    'type' => 'string',
                ],
                [
                    'code' => 'app.accessTokenVimeo',
                    'value' => '',
                    'name' => 'Access token',
                    'sort' => 3,
                    'options' => [],
                    'type' => 'string',
                ],
                [
                    'code' => 'app.userIdVimeo',
                    'value' => '',
                    'name' => 'User Id',
                    'sort' => 4,
                    'options' => [],
                    'type' => 'string',
                ],
                [
                    'code' => 'app.projectIdVimeo',
                    'value' => '',
                    'name' => 'Project Id',
                    'sort' => 5,
                    'options' => [],
                    'type' => 'string',
                ],
                [
                    'code' => 'app.projectIdResourceCourse',
                    'value' => '',
                    'name' => 'Project Id Resource Course',
                    'sort' => 6,
                    'options' => [],
                    'type' => 'string',
                ],
                [
                    'code' => 'app.projectIdTaskCourse',
                    'value' => '',
                    'name' => 'Project Id Task Course',
                    'sort' => 7,
                    'options' => [],
                    'type' => 'string',
                ],
                [
                    'code' => 'app.projectIdVideoQuiz',
                    'value' => '',
                    'name' => 'Project Id Video Quiz',
                    'sort' => 8,
                    'options' => [],
                    'type' => 'string',
                ],
                [
                    'code' => 'app.projectIdRoleplay',
                    'value' => '',
                    'name' => 'Project Id Roleplay',
                    'sort' => 9,
                    'options' => [],
                    'type' => 'string',
                ],
                [
                    'code' => 'app.uploadSubdomain',
                    'value' => '',
                    'name' => 'Upload Subdomain',
                    'sort' => 10,
                    'options' => [],
                    'type' => 'string',
                ],
            ],
        ],
        [
            'group' => 'Email',
            'settings' => [
                [
                    'code' => 'app.fromEmail',
                    'value' => '<EMAIL>',
                    'name' => 'From Email',
                    'sort' => 1,
                    'options' => [],
                    'type' => 'string',
                ],
                [
                    'code' => 'app.fromName',
                    'value' => '',
                    'name' => 'From Name',
                    'sort' => 2,
                    'options' => [],
                    'type' => 'string',
                ],
                [
                    'code' => 'app.fromCIF',
                    'value' => '2342343Y',
                    'name' => 'From CIF',
                    'sort' => 3,
                    'options' => [],
                    'type' => 'string',
                ],
            ],
        ],
        [
            'group' => 'Ayuda',
            'settings' => [],
        ],
        [
            'group' => 'Estadísticas',
            'settings' => [],
        ],
        [
            'group' => 'Módulos',
            'settings' => [
                [
                    'code' => 'app.news.enabled',
                    'value' => 'false',
                    'name' => 'News',
                    'sort' => 1,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.forum.enabled',
                    'value' => 'false',
                    'name' => 'Foro',
                    'sort' => 2,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.challenge.enabled',
                    'value' => 'false',
                    'name' => 'Desafíos',
                    'sort' => 3,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.sharefile.enabled',
                    'value' => 'false',
                    'name' => 'Sharefile',
                    'sort' => 4,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.course_section.enabled',
                    'value' => 'true',
                    'name' => 'Secciones',
                    'sort' => 5,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.course.materials.enabled',
                    'value' => 'false',
                    'name' => 'Course materials',
                    'sort' => 6,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.course.tasks.enabled',
                    'value' => 'false',
                    'name' => 'Course tasks',
                    'sort' => 7,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.course.onSite',
                    'value' => 'false',
                    'name' => 'Course onSite',
                    'sort' => 8,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.course.survey',
                    'value' => 'false',
                    'name' => 'Encuestas',
                    'sort' => 9,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.documentation.enabled',
                    'value' => 'false',
                    'name' => 'Documentación',
                    'sort' => 10,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.user_company.enabled',
                    'value' => 'false',
                    'name' => 'Empresas',
                    'sort' => 11,
                    'options' => [],
                    'type' => 'bool',
                ],
            ],
        ],
        [
            'group' => 'Otros',
            'settings' => [
                [
                    'code' => 'app.useSegment',
                    'value' => 'true',
                    'name' => 'Use Segment',
                    'sort' => 1,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.showDeactivatedCourses',
                    'value' => 'false',
                    'name' => 'Show Deactivated Courses',
                    'sort' => 2,
                    'options' => [],
                    'type' => 'bool',
                ],
                [
                    'code' => 'app.froalaKey',
                    'value' => '',
                    'name' => 'Froala Key',
                    'sort' => 3,
                    'options' => [],
                    'type' => 'string',
                ],
                [
                    'code' => 'app.subsidizer.active',
                    'value' => 'false',
                    'name' => 'Subsidizer active',
                    'sort' => 4,
                    'options' => [],
                    'type' => 'bool',
                ],
            ],
        ],
    ];
}
