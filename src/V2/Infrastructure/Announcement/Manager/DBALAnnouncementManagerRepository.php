<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Announcement\Manager;

use App\V2\Domain\Announcement\Manager\AnnouncementManager;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerCollection;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerCriteria;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerNotFoundException;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerRepository;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerRepositoryException;
use App\V2\Domain\Shared\Id\Id;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception as DBALException;
use Doctrine\DBAL\Query\QueryBuilder;
use Doctrine\ORM\EntityManagerInterface;

class DBALAnnouncementManagerRepository implements AnnouncementManagerRepository
{
    private Connection $connection;

    public function __construct(
        EntityManagerInterface $em,
        private readonly string $announcementManagerTableName,
    ) {
        $this->connection = $em->getConnection();
    }

    #[\Override]
    public function insert(AnnouncementManager $announcementManager): void
    {
        try {
            $this->findOneBy(
                AnnouncementManagerCriteria::createEmpty()
                    ->filterByUserId($announcementManager->getUserId()->value())
                    ->filterByAnnouncementId($announcementManager->getAnnouncementId()->value())
            );

            throw AnnouncementManagerRepositoryException::duplicateAnnouncementManager($announcementManager);
        } catch (AnnouncementManagerNotFoundException) {
        }
        try {
            $this->connection
                ->insert(
                    table: $this->announcementManagerTableName,
                    data: $this->fromAnnouncementManagerToArray($announcementManager),
                );
        } catch (DBALException $e) {
            throw AnnouncementManagerRepositoryException::fromPrevious($e);
        }
    }

    #[\Override]
    public function findOneBy(AnnouncementManagerCriteria $criteria): AnnouncementManager
    {
        try {
            $result = $this->getQueryBuilderByCriteria($criteria)
                ->executeQuery()
                ->fetchAssociative();

            if (false === $result) {
                throw new AnnouncementManagerNotFoundException();
            }

            return $this->fromArrayToAnnouncementManager($result);
        } catch (DBALException $e) {
            throw AnnouncementManagerRepositoryException::fromPrevious($e);
        }
    }

    #[\Override]
    public function findBy(AnnouncementManagerCriteria $criteria): AnnouncementManagerCollection
    {
        try {
            $result = $this->getQueryBuilderByCriteria($criteria)
                ->executeQuery()
                ->fetchAllAssociative();

            return new AnnouncementManagerCollection(
                array_map(
                    fn (array $values) => $this->fromArrayToAnnouncementManager($values),
                    $result,
                )
            );
        } catch (DBALException $e) {
            throw AnnouncementManagerRepositoryException::fromPrevious($e);
        }
    }

    #[\Override]
    public function delete(AnnouncementManager $announcementManager): void
    {
        try {
            $this->connection
                ->delete(
                    table: $this->announcementManagerTableName,
                    criteria: [
                        'user_id' => $announcementManager->getUserId()->value(),
                        'announcement_id' => $announcementManager->getAnnouncementId()->value(),
                    ]
                );
        } catch (DBALException $e) {
            throw AnnouncementManagerRepositoryException::fromPrevious($e);
        }
    }

    private function fromAnnouncementManagerToArray(AnnouncementManager $announcementManager): array
    {
        return [
            'user_id' => $announcementManager->getUserId(),
            'announcement_id' => $announcementManager->getAnnouncementId(),
        ];
    }

    private function fromArrayToAnnouncementManager(array $values): AnnouncementManager
    {
        return new AnnouncementManager(
            userId: new Id($values['user_id']),
            announcementId: new Id($values['announcement_id']),
        );
    }

    private function getQueryBuilderByCriteria(AnnouncementManagerCriteria $criteria): QueryBuilder
    {
        $queryBuilder = $this->connection->createQueryBuilder()
            ->select('t.*')
            ->from($this->announcementManagerTableName, 't');

        // Join with user table if sorting is needed
        $sortBy = $criteria->getSortBy();
        if (null !== $sortBy) {
            $needsUserJoin = false;
            foreach ($sortBy->all() as $sortByItem) {
                $field = $sortByItem->getField()->value();
                if (in_array($field, ['firstName', 'lastName'])) {
                    $needsUserJoin = true;
                    break;
                }
            }

            if ($needsUserJoin) {
                $queryBuilder->leftJoin('t', 'user', 'u', 't.user_id = u.id');
            }
        }

        if (null !== $criteria->getUserId()) {
            $queryBuilder->andWhere('t.user_id = :userId')
                ->setParameter('userId', $criteria->getUserId());
        }

        if (null !== $criteria->getAnnouncementId()) {
            $queryBuilder->andWhere('t.announcement_id = :announcementId')
                ->setParameter('announcementId', $criteria->getAnnouncementId());
        }

        // Apply sorting
        if (null !== $sortBy) {
            foreach ($sortBy->all() as $sortByItem) {
                $field = $sortByItem->getField()->value();
                $direction = $sortByItem->getDirection() === \App\V2\Domain\Shared\Criteria\SortDirection::ASC ? 'ASC' : 'DESC';

                switch ($field) {
                    case 'firstName':
                        $queryBuilder->addOrderBy('u.first_name', $direction);
                        break;
                    case 'lastName':
                        $queryBuilder->addOrderBy('u.last_name', $direction);
                        break;
                    case 'userId':
                        $queryBuilder->addOrderBy('t.user_id', $direction);
                        break;
                    case 'announcementId':
                        $queryBuilder->addOrderBy('t.announcement_id', $direction);
                        break;
                    default:
                        // Default sorting by user_id if unknown field
                        $queryBuilder->addOrderBy('t.user_id', 'ASC');
                }
            }
        } else {
            // Default sorting
            $queryBuilder->orderBy('t.user_id', 'ASC');
        }

        return $queryBuilder;
    }
}
