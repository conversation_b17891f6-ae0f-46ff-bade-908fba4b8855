<?php

declare(strict_types=1);

namespace App\V2\Application\QueryHandler\Admin;

use App\Repository\AnnouncementRepository as LegacyAnnouncementRepository;
use App\V2\Application\Hydrator\Announcement\Manager\AnnouncementManagerHydratorCollection;
use App\V2\Application\Query\Admin\GetAnnouncementManagers;
use App\V2\Domain\Announcement\Exceptions\AnnouncementNotFoundException;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerCollection;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerHydrationCriteria;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerRepository;
use App\V2\Domain\Announcement\Manager\Exceptions\GetAnnouncementManagerException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Hydrator\HydratorException;

readonly class GetAnnouncementManagersHandler
{
    public function __construct(
        private AnnouncementManagerRepository $announcementManagerRepository,
        private LegacyAnnouncementRepository $legacyAnnouncementRepository,
        private AnnouncementManagerHydratorCollection $hydratorCollection,
    ) {
    }

    /**
     * @throws AnnouncementNotFoundException
     * @throws InfrastructureException
     * @throws GetAnnouncementManagerException
     */
    public function handle(GetAnnouncementManagers $query): AnnouncementManagerCollection
    {
        $criteria = $query->getCriteria();

        $announcement = $this->legacyAnnouncementRepository->findOneBy(['id' => $criteria->getAnnouncementId()]);
        if (null === $announcement) {
            throw new AnnouncementNotFoundException();
        }

        $announcementManagerCollection = $this->announcementManagerRepository
            ->findBy($criteria);

        $hydrationCriteria = AnnouncementManagerHydrationCriteria::createEmpty();

        if ($query->needsManagers()) {
            $hydrationCriteria->withUser();
        }

        if (!$hydrationCriteria->isEmpty()) {
            try {
                $this->hydratorCollection->hydrate(
                    collection: $announcementManagerCollection,
                    criteria: $hydrationCriteria,
                );
            } catch (HydratorException $e) {
                throw GetAnnouncementManagerException::fromPrevious($e);
            }
        }

        return $announcementManagerCollection;
    }
}
