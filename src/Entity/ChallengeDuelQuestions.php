<?php

namespace App\Entity;

use App\Repository\ChallengeDuelQuestionsRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;

/**
 * @ORM\Entity(repositoryClass=ChallengeDuelQuestionsRepository::class)
 */
class ChallengeDuelQuestions
{
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Groups({"bot"})
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity=ChallengeDuel::class, inversedBy="challengeDuelQuestions")
     * @ORM\JoinColumn(nullable=false)
     */
    private $duel;

    /**
     * @ORM\ManyToOne(targetEntity=ChallengeQuestions::class, inversedBy="challengeQuestionsDuel")
     * @Groups({"duelQuestions","bot"})
     */
    private $question;

    /**
     * @ORM\ManyToOne(targetEntity=ChallengeAnswers::class, inversedBy="challengeAnswers1Duel")
     * @Groups({"bot"})
     */
    private $answerUser1;

    /**
     * @ORM\ManyToOne(targetEntity=ChallengeAnswers::class, inversedBy="challengeAnswers2Duel")
     */
    private $answerUser2;

    /**
     * @ORM\Column(type="integer",nullable=true)
     */
    private $timeUser1;

    /**
     * @ORM\Column(type="integer",nullable=true)
     */
    private $timeUser2;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getDuel(): ?ChallengeDuel
    {
        return $this->duel;
    }

    public function setDuel(?ChallengeDuel $duel): self
    {
        $this->duel = $duel;

        return $this;
    }

    public function getQuestion(): ?ChallengeQuestions
    {
        return $this->question;
    }

    public function setQuestion(ChallengeQuestions $question): self
    {
        $this->question = $question;

        return $this;
    }

    public function getAnswerUser1Id(): ?ChallengeAnswers
    {
        return $this->answerUser1;
    }

    public function setAnswerUser1Id(ChallengeAnswers $answerUser1): self
    {
        $this->answerUser1 = $answerUser1;

        return $this;
    }

    public function getAnswerUser2Id(): ?ChallengeAnswers
    {
        return $this->answerUser2;
    }

    public function setAnswerUser2Id(?ChallengeAnswers $answerUser2): self
    {
        $this->answerUser2 = $answerUser2;

        return $this;
    }

    public function getTimeUser1(): ?int
    {
        return $this->timeUser1;
    }

    public function setTimeUser1(int $timeUser1): self
    {
        $this->timeUser1 = $timeUser1;

        return $this;
    }

    public function getTimeUser2(): ?int
    {
        return $this->timeUser2;
    }

    public function setTimeUser2(int $timeUser2): self
    {
        $this->timeUser2 = $timeUser2;

        return $this;
    }
}
