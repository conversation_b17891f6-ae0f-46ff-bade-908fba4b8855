<script>
import BaseSwitch from "../../../base/BaseSwitch.vue";
import Spinner from "../../../admin/components/base/Spinner.vue";
import {get, sync} from "vuex-pathify";

export default {
  name: "ConfigurationClientAnnouncement",
  components: {BaseSwitch, Spinner},
  computed: {
    loading: get('catalogModule/loading'),
    catalogs: sync('catalogModule/catalogs'),
  },

  data() {
    return {
      activeIndex: -1
    };
  },

  created() {
    this.$store.dispatch('catalogModule/load', '/admin/configuration-client-announcement/all');
  },
  methods: {
    changeActiveStatus(index) {
      const value = this.catalogs[index];
      this.$store.dispatch('catalogModule/changeActiveState',
          {endpoint: `/admin/configuration-client-announcement/${value.id}/active`, data: {id: value.id, active: value.active}}).then(r => {

      });
    },
    changeSubconfigActiveStatus(indexCatalog, indexSubconfig) {
      const config = this.catalogs[indexCatalog];
      const subConfig = config.configs[indexSubconfig];
      this.$store.dispatch('catalogModule/changeActiveState',
          {endpoint: `/admin/configuration-client-announcement/${config.id}/subconfig/${subConfig.id}/active`, data: {id: subConfig.id, active: subConfig.active}}).then(r => {

      });
    }
  }
}
</script>

<template>
  <div>
    <div class="col-12 d-flex flex-row align-items-center justify-content-center" v-if="loading">
      <spinner />
    </div>
    <table class="table table-condensed mt-3" v-else>
      <thead>
      <tr> 
        <th></th>
        <th>{{ $t('NAME') }}</th>
        <th>{{ $t('ACTIVE') }}</th>
        <th></th>
        <th>  </th>
        <th></th>
      </tr>
      </thead>
      <tbody v-for="(config, index) in catalogs" :key="config.id">
      <tr>
        <td></td>
        <td>{{ config.name }}</td>
        <td>
          <BaseSwitch :tag="`switcher-configuration-client-${config.id}`" v-model="config.active" @change="changeActiveStatus(index)" />
        </td>
        <td>{{ config.description }}</td>
        <td>
          <span @click="activeIndex = index" class="fa" :class="activeIndex === index ? 'fa-caret-up' : 'fa-caret-down'"></span>
        </td>  
      </tr>
      <tr v-if="activeIndex === index">
        <td colspan="5">
          <table class="table table-condensed subconfig">
            <thead>
            <tr>
              <th class="text-center" colspan="3"><strong>Configuraciones por sección</strong></th>
            </tr>
            <tr>
              <th>Nombre</th>
              <th>Descripción</th>
              <th>Activo</th>
              <th></th>
            </tr>
            </thead>
            <tbody>
            <tr v-for="(subConfig, indexSubConfig) in config.configs" :key="`subconfig-${subConfig.id}`">
              <td>{{ subConfig.name }}</td>
              <td>{{ subConfig.description }}</td>
              <td>
                <BaseSwitch :tag="`switcher-configuration-client-subconfig-${subConfig.id}`" v-model="subConfig.active" @change="changeSubconfigActiveStatus(index, indexSubConfig)" />
              </td>           
              <td>
                <div class="dropdown">
                  <button class="btn btn-default" type="button" :id="`dropdown-menu-${subConfig.id}`" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fa fa-ellipsis-h"></i>
                  </button>
                  <ul class="dropdown-menu" :aria-labelledby="`dropdown-menu-${subConfig.id}`">
                    <li><router-link class="dropdown-item" :to="{ name: 'ConfigurationClientAnnouncementUpdate', params: {...$route.params, id: config.id, idtype:subConfig.id} }">{{ $t('EDIT') }}</router-link></li>
                  </ul>
                </div>
              </td>
            </tr>
            </tbody>
          </table>
        </td>
      </tr>
      </tbody>
    </table>
  </div>
</template>

<style scoped lang="scss">
table {
  &.subconfig {
    border: 1px dashed var(--color-primary);
    tr, td, th {
      border: none;
    }
  }
}

.table>:not(:first-child) {
    border-top: none !important;
}
</style>
